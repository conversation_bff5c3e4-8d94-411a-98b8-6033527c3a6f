#!/bin/bash

# Remote Deployment Script for FocusBudget
# This script packages the project, transfers it to a remote machine, and triggers deployment

# Exit on error
set -e

# Colors for better readability
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

# Configuration
REMOTE_USER="labmaster"
REMOTE_HOST="**********"
REMOTE_PATH="/home/<USER>/focusbudget"
PROJECT_DIR="/BudgetPage/project"
ARCHIVE_NAME="focusbudget-$(date +%Y%m%d-%H%M%S).zip"

echo -e "\n${BLUE}==============================="
echo -e " FocusBudget Remote Deployment"
echo -e "===============================${NC}\n"

# Check if we're in the right location
if [ ! -d "$PROJECT_DIR" ]; then
  echo -e "${RED}[!] Project directory not found: $PROJECT_DIR${NC}"
  exit 1
fi

# Check if required files exist
if [ ! -f "$PROJECT_DIR/build-and-deploy.sh" ]; then
  echo -e "${RED}[!] build-and-deploy.sh not found in project directory${NC}"
  exit 1
fi

if [ ! -f "$PROJECT_DIR/package.json" ]; then
  echo -e "${RED}[!] package.json not found in project directory${NC}"
  exit 1
fi

# Check if zip is available
if ! command -v zip >/dev/null 2>&1; then
  echo -e "${RED}[!] zip command not found. Please install zip utility.${NC}"
  echo -e "${YELLOW}[i] Ubuntu/Debian: sudo apt-get install zip${NC}"
  echo -e "${YELLOW}[i] RHEL/CentOS: sudo yum install zip${NC}"
  exit 1
fi

# Check SSH connectivity and determine authentication method
echo -e "${BLUE}[>] Testing SSH connectivity to ${REMOTE_USER}@${REMOTE_HOST}${NC}"

# First try with SSH key (no password)
if ssh -o ConnectTimeout=10 -o BatchMode=yes "${REMOTE_USER}@${REMOTE_HOST}" exit 2>/dev/null; then
  echo -e "${GREEN}[✓] SSH key authentication successful${NC}"
  SSH_AUTH_METHOD="key"
else
  echo -e "${YELLOW}[i] SSH key authentication not available${NC}"
  echo -e "${BLUE}[>] Testing password authentication${NC}"
  echo -e "${YELLOW}[i] You will be prompted for the password for ${REMOTE_USER}@${REMOTE_HOST}${NC}"
  
  # Test basic connectivity with password prompt
  if ! ssh -o ConnectTimeout=10 "${REMOTE_USER}@${REMOTE_HOST}" exit; then
    echo -e "${RED}[!] Cannot connect to ${REMOTE_USER}@${REMOTE_HOST}${NC}"
    echo -e "${YELLOW}[i] Please ensure:${NC}"
    echo -e "${YELLOW}  1. Remote host is accessible${NC}"
    echo -e "${YELLOW}  2. Username and password are correct${NC}"
    echo -e "${YELLOW}  3. SSH service is running on remote host${NC}"
    exit 1
  fi
  
  echo -e "${GREEN}[✓] SSH password authentication confirmed${NC}"
  echo -e "${YELLOW}[i] Note: You will be prompted for password several times during deployment${NC}"
  SSH_AUTH_METHOD="password"
fi

# Create temporary directory for packaging
TEMP_DIR=$(mktemp -d)
echo -e "${BLUE}[>] Creating temporary directory: $TEMP_DIR${NC}"

# Copy project to temp directory (excluding node_modules and other build artifacts)
echo -e "${BLUE}[>] Preparing project files for packaging${NC}"
rsync -av --exclude='node_modules' \
          --exclude='dist' \
          --exclude='.git' \
          --exclude='*.log' \
          --exclude='logs' \
          --exclude='data' \
          --exclude='backup-*' \
          --exclude='*.zip' \
          --exclude='test-*.html' \
          --exclude='test-*.js' \
          "$PROJECT_DIR/" "$TEMP_DIR/focusbudget/"

echo -e "${GREEN}[✓] Project files prepared${NC}"

# Create zip archive
echo -e "${BLUE}[>] Creating archive: $ARCHIVE_NAME${NC}"
cd "$TEMP_DIR"
zip -r "$ARCHIVE_NAME" focusbudget/ >/dev/null
echo -e "${GREEN}[✓] Archive created ($(du -h "$ARCHIVE_NAME" | cut -f1))${NC}"

# Transfer archive to remote machine
echo -e "${BLUE}[>] Transferring archive to ${REMOTE_USER}@${REMOTE_HOST}${NC}"
if [ "$SSH_AUTH_METHOD" = "password" ]; then
  echo -e "${YELLOW}[i] Please enter password for file transfer:${NC}"
fi
scp "$ARCHIVE_NAME" "${REMOTE_USER}@${REMOTE_HOST}:/tmp/"
echo -e "${GREEN}[✓] Archive transferred successfully${NC}"

# Create deployment script for remote execution
REMOTE_SCRIPT="/tmp/remote-deploy-commands.sh"
cat > "$TEMP_DIR/remote-commands.sh" << 'EOF'
#!/bin/bash

# Remote deployment commands for FocusBudget
set -e

ARCHIVE_NAME="$1"
REMOTE_PATH="$2"

echo "Starting FocusBudget remote deployment..."

# Create deployment directory
mkdir -p "$REMOTE_PATH"

# Backup existing deployment if it exists
if [ -d "$REMOTE_PATH/focusbudget" ]; then
  echo "Backing up existing deployment..."
  mv "$REMOTE_PATH/focusbudget" "$REMOTE_PATH/focusbudget-backup-$(date +%Y%m%d-%H%M%S)"
fi

# Extract new deployment
echo "Extracting new deployment..."
cd "$REMOTE_PATH"
unzip -q "/tmp/$ARCHIVE_NAME"

# Make scripts executable
chmod +x "$REMOTE_PATH/focusbudget/build-and-deploy.sh"
chmod +x "$REMOTE_PATH/focusbudget/test-deployment.sh"

# Change to project directory
cd "$REMOTE_PATH/focusbudget"

# Run deployment
echo "Starting build and deployment..."
./build-and-deploy.sh

echo "FocusBudget remote deployment completed successfully!"
EOF

# Transfer and execute remote script
echo -e "${BLUE}[>] Executing deployment on remote machine${NC}"
if [ "$SSH_AUTH_METHOD" = "password" ]; then
  echo -e "${YELLOW}[i] Please enter password for script transfer:${NC}"
fi
scp "$TEMP_DIR/remote-commands.sh" "${REMOTE_USER}@${REMOTE_HOST}:$REMOTE_SCRIPT"

# Execute remote deployment
echo -e "${BLUE}[>] Running build and deployment on remote server${NC}"
if [ "$SSH_AUTH_METHOD" = "password" ]; then
  echo -e "${YELLOW}[i] Please enter password for deployment execution:${NC}"
fi
ssh "${REMOTE_USER}@${REMOTE_HOST}" "chmod +x $REMOTE_SCRIPT && $REMOTE_SCRIPT '$ARCHIVE_NAME' '$REMOTE_PATH'"

# Test remote deployment
echo -e "\n${BLUE}[>] Testing remote deployment${NC}"
if [ "$SSH_AUTH_METHOD" = "password" ]; then
  echo -e "${YELLOW}[i] Please enter password for deployment testing:${NC}"
fi
ssh "${REMOTE_USER}@${REMOTE_HOST}" "cd '$REMOTE_PATH/focusbudget' && ./test-deployment.sh"

# Cleanup
echo -e "\n${BLUE}[>] Cleaning up temporary files${NC}"
rm -rf "$TEMP_DIR"
if [ "$SSH_AUTH_METHOD" = "password" ]; then
  echo -e "${YELLOW}[i] Please enter password for cleanup:${NC}"
fi
ssh "${REMOTE_USER}@${REMOTE_HOST}" "rm -f /tmp/$ARCHIVE_NAME $REMOTE_SCRIPT"

# Final status
echo -e "\n${GREEN}==============================="
echo -e " FocusBudget Deployment Complete!"
echo -e "===============================${NC}"
echo -e "${GREEN}[✓] Project packaged and transferred${NC}"
echo -e "${GREEN}[✓] Remote build and deployment successful${NC}"
echo -e "${GREEN}[✓] Services started and tested${NC}"

echo -e "\n${BLUE}Access Points:${NC}"
echo -e "${YELLOW}• Primary: http://${REMOTE_HOST}${NC}"
echo -e "${YELLOW}• Direct: http://${REMOTE_HOST}:3001${NC}"
echo -e "${YELLOW}• API: http://${REMOTE_HOST}/api/storage/status${NC}"

echo -e "\n${BLUE}Remote Management:${NC}"
echo -e "${YELLOW}• SSH access: ssh ${REMOTE_USER}@${REMOTE_HOST}${NC}"
echo -e "${YELLOW}• Project location: ${REMOTE_PATH}/focusbudget${NC}"
echo -e "${YELLOW}• Service status: ssh ${REMOTE_USER}@${REMOTE_HOST} 'sudo systemctl status budgetpage'${NC}"
echo -e "${YELLOW}• Service logs: ssh ${REMOTE_USER}@${REMOTE_HOST} 'sudo journalctl -u budgetpage -f'${NC}"
echo -e "${YELLOW}• Nginx status: ssh ${REMOTE_USER}@${REMOTE_HOST} 'sudo systemctl status nginx'${NC}"

echo -e "\n${BLUE}Quick Commands:${NC}"
echo -e "${YELLOW}• Restart services: ssh ${REMOTE_USER}@${REMOTE_HOST} 'sudo systemctl restart budgetpage nginx'${NC}"
echo -e "${YELLOW}• Check deployment: ssh ${REMOTE_USER}@${REMOTE_HOST} 'cd ${REMOTE_PATH}/focusbudget && ./test-deployment.sh'${NC}"
echo -e "${YELLOW}• View logs: ssh ${REMOTE_USER}@${REMOTE_HOST} 'sudo journalctl -u budgetpage -f'${NC}"

echo -e "\n${GREEN}FocusBudget is now running on the remote server!${NC}"
