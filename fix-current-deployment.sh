#!/bin/bash

# Quick fix for the current deployment issue
# This script fixes the immediate problems without full redeployment

# Colors for better readability
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

# Configuration
REMOTE_HOST="**********"
REMOTE_USER="labmaster"
REMOTE_TARGET="${REMOTE_USER}@${REMOTE_HOST}"

echo -e "\n${BLUE}==============================="
echo -e " Quick Fix for Current Deployment"
echo -e "===============================${NC}\n"

echo -e "${BLUE}[>] Fixing the current deployment issues${NC}"

# 1. Fix the server.cjs file with correct route order
echo -e "\n${YELLOW}[i] Step 1: Fixing server.cjs route order${NC}"
scp server.cjs "${REMOTE_TARGET}:/tmp/server.cjs.fixed"

ssh "${REMOTE_TARGET}" "
  echo 'Stopping focusbudget service...'
  sudo systemctl stop focusbudget 2>/dev/null || true
  
  echo 'Backing up current server.cjs...'
  sudo cp /opt/focusbudget/server.cjs /opt/focusbudget/server.cjs.backup 2>/dev/null || true
  
  echo 'Installing fixed server.cjs...'
  sudo cp /tmp/server.cjs.fixed /opt/focusbudget/server.cjs
  sudo chown focusbudget_user:focusbudget_group /opt/focusbudget/server.cjs 2>/dev/null || sudo chown \$(whoami):\$(whoami) /opt/focusbudget/server.cjs
  sudo chmod 644 /opt/focusbudget/server.cjs
  
  echo 'Cleaning up temp file...'
  rm -f /tmp/server.cjs.fixed
"

echo -e "${GREEN}[✓] Server.cjs fixed${NC}"

# 2. Ensure Nginx is properly configured
echo -e "\n${YELLOW}[i] Step 2: Checking and fixing Nginx configuration${NC}"
ssh "${REMOTE_TARGET}" "
  # Install Nginx if not present
  if ! command -v nginx >/dev/null 2>&1; then
    echo 'Installing Nginx...'
    sudo apt-get update
    sudo apt-get install -y nginx
  fi
  
  # Create basic reverse proxy config if missing
  if [ ! -f /etc/nginx/sites-available/focusbudget ]; then
    echo 'Creating Nginx configuration...'
    sudo tee /etc/nginx/sites-available/focusbudget > /dev/null << 'EOF'
server {
    listen 80;
    listen [::]:80;
    server_name _;

    location / {
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }

    location /api/ {
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    location /health {
        proxy_pass http://127.0.0.1:3001/api/status;
        proxy_set_header Host \$host;
        access_log off;
    }
}
EOF
  fi
  
  # Enable the site
  sudo mkdir -p /etc/nginx/sites-enabled
  sudo ln -sf /etc/nginx/sites-available/focusbudget /etc/nginx/sites-enabled/focusbudget
  
  # Remove default site if it exists
  sudo rm -f /etc/nginx/sites-enabled/default
  
  # Test and restart Nginx
  if sudo nginx -t; then
    echo 'Nginx configuration is valid'
    sudo systemctl enable nginx
    sudo systemctl restart nginx
  else
    echo 'Nginx configuration test failed'
  fi
"

echo -e "${GREEN}[✓] Nginx configuration checked${NC}"

# 3. Start the FocusBudget service
echo -e "\n${YELLOW}[i] Step 3: Starting FocusBudget service${NC}"
ssh "${REMOTE_TARGET}" "
  echo 'Starting focusbudget service...'
  sudo systemctl start focusbudget
  
  # Wait a moment for startup
  sleep 3
  
  echo 'Service status:'
  sudo systemctl status focusbudget --no-pager || true
"

# 4. Test the deployment
echo -e "\n${YELLOW}[i] Step 4: Testing the deployment${NC}"
sleep 2

ssh "${REMOTE_TARGET}" "
  echo 'Testing port 80 (Nginx):'
  curl -s -I http://localhost/ | head -3 || echo 'Port 80 test failed'
  
  echo ''
  echo 'Testing port 3001 (direct backend):'
  curl -s -I http://localhost:3001/ | head -3 || echo 'Port 3001 test failed'
  
  echo ''
  echo 'Testing health endpoint:'
  curl -s http://localhost/health || curl -s http://localhost:3001/api/status || echo 'Health check failed'
  
  echo ''
  echo 'Testing API status:'
  curl -s http://localhost/api/status || curl -s http://localhost:3001/api/status || echo 'API status failed'
  
  echo ''
  echo 'Checking what is listening on ports:'
  sudo netstat -tlnp | grep -E ':(80|3001) '
"

echo -e "\n${GREEN}==============================="
echo -e " Quick Fix Complete!"
echo -e "===============================${NC}"

echo -e "\n${BLUE}Results:${NC}"
echo -e "${YELLOW}• Fixed server.cjs route order issue${NC}"
echo -e "${YELLOW}• Ensured Nginx reverse proxy is configured${NC}"
echo -e "${YELLOW}• Restarted services${NC}"
echo -e "${YELLOW}• Tested connectivity${NC}"

echo -e "\n${BLUE}Access Points:${NC}"
echo -e "${YELLOW}• Primary: http://${REMOTE_HOST}${NC}"
echo -e "${YELLOW}• Health: http://${REMOTE_HOST}/health${NC}"
echo -e "${YELLOW}• API Status: http://${REMOTE_HOST}/api/status${NC}"

echo -e "\n${BLUE}If issues persist:${NC}"
echo -e "${YELLOW}• Check service: ssh ${REMOTE_TARGET} 'sudo systemctl status focusbudget'${NC}"
echo -e "${YELLOW}• Check logs: ssh ${REMOTE_TARGET} 'sudo journalctl -u focusbudget -f'${NC}"
echo -e "${YELLOW}• Check Nginx: ssh ${REMOTE_TARGET} 'sudo systemctl status nginx'${NC}"
echo -e "${YELLOW}• Run full redeployment: ./deploy-to-remote-simple.sh${NC}"

echo -e "\n${GREEN}Quick fix completed!${NC}"
