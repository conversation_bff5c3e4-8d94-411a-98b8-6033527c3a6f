#!/bin/bash

# FocusBudget Complete Deployment Script
# Handles ALL dependencies and tools required for deployment

# Exit on error
set -e

# Colors for better readability
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

# Configuration
REMOTE_HOST="**********"
REMOTE_USER="labmaster"
REMOTE_TARGET="${REMOTE_USER}@${REMOTE_HOST}"
LOCAL_PROJECT_DIR="$(pwd)/project"
APP_NAME="focusbudget"
APP_USER="${APP_NAME}_user"
APP_GROUP="${APP_NAME}_group"
APP_ROOT="/opt/${APP_NAME}"
DATA_ROOT="/var/lib/${APP_NAME}"
LOG_ROOT="/var/log/${APP_NAME}"

echo -e "\n${BLUE}==============================="
echo -e " FocusBudget Complete Deployment"
echo -e "===============================${NC}\n"

echo -e "${BLUE}Configuration:${NC}"
echo -e "${YELLOW}• Remote server: ${REMOTE_HOST}${NC}"
echo -e "${YELLOW}• Remote user: ${REMOTE_USER}${NC}"
echo -e "${YELLOW}• Application: ${APP_NAME}${NC}"
echo -e "${YELLOW}• Service user: ${APP_USER}${NC}"
echo -e "${YELLOW}• App directory: ${APP_ROOT}${NC}"
echo -e "${YELLOW}• Data directory: ${DATA_ROOT}${NC}"

# Step 1: Install ALL local prerequisites
echo -e "\n${BLUE}[1/10] Installing ALL local prerequisites${NC}"

MISSING_TOOLS=()

# Check for required tools
for tool in zip ssh rsync curl wget node npm; do
  if ! command -v "$tool" >/dev/null 2>&1; then
    MISSING_TOOLS+=("$tool")
  fi
done

if [ ${#MISSING_TOOLS[@]} -gt 0 ]; then
  echo -e "${YELLOW}[i] Installing missing tools: ${MISSING_TOOLS[*]}${NC}"
  
  if command -v apt-get >/dev/null 2>&1; then
    sudo apt-get update
    # Map tools to package names
    PACKAGES=()
    for tool in "${MISSING_TOOLS[@]}"; do
      case "$tool" in
        "zip") PACKAGES+=("zip") ;;
        "ssh") PACKAGES+=("openssh-client") ;;
        "rsync") PACKAGES+=("rsync") ;;
        "curl") PACKAGES+=("curl") ;;
        "wget") PACKAGES+=("wget") ;;
        "node"|"npm") PACKAGES+=("nodejs" "npm") ;;
      esac
    done
    sudo apt-get install -y "${PACKAGES[@]}"
  else
    echo -e "${RED}[!] Cannot install prerequisites automatically${NC}"
    echo -e "${YELLOW}[i] Please install: ${MISSING_TOOLS[*]}${NC}"
    exit 1
  fi
else
  echo -e "${GREEN}[✓] All local prerequisites are available${NC}"
fi

# Step 2: Validate local project
echo -e "\n${BLUE}[2/10] Validating local project${NC}"

if [ ! -f "${LOCAL_PROJECT_DIR}/package.json" ]; then
  echo -e "${RED}[!] package.json not found in ${LOCAL_PROJECT_DIR}${NC}"
  exit 1
fi

if [ ! -f "${LOCAL_PROJECT_DIR}/server.cjs" ]; then
  echo -e "${RED}[!] server.cjs not found in ${LOCAL_PROJECT_DIR}${NC}"
  exit 1
fi

if [ ! -d "${LOCAL_PROJECT_DIR}/src" ]; then
  echo -e "${RED}[!] src directory not found in ${LOCAL_PROJECT_DIR}${NC}"
  exit 1
fi

# Get version from package.json
APP_VERSION=$(grep '"version"' "${LOCAL_PROJECT_DIR}/package.json" | cut -d'"' -f4)
echo -e "${GREEN}[✓] Project validated - Version: ${APP_VERSION}${NC}"

# Step 3: Setup SSH keys if needed
echo -e "\n${BLUE}[3/10] Setting up SSH authentication${NC}"

if ssh -o ConnectTimeout=10 -o BatchMode=yes "${REMOTE_TARGET}" "echo 'SSH key auth works'" 2>/dev/null; then
  echo -e "${GREEN}[✓] SSH key authentication is working${NC}"
else
  echo -e "${YELLOW}[!] SSH key authentication not available${NC}"
  echo -e "${YELLOW}[i] Setting up SSH key authentication...${NC}"
  
  if [ ! -f ~/.ssh/id_rsa.pub ] && [ ! -f ~/.ssh/id_ed25519.pub ]; then
    echo -e "${YELLOW}[i] No SSH key found. Generating new SSH key...${NC}"
    read -p "Enter your email for SSH key: " user_email
    ssh-keygen -t ed25519 -C "$user_email" -f ~/.ssh/id_ed25519 -N ""
    echo -e "${GREEN}[✓] SSH key generated${NC}"
  fi
  
  echo -e "${YELLOW}[i] Copying SSH key to remote server...${NC}"
  if [ -f ~/.ssh/id_ed25519.pub ]; then
    ssh-copy-id -i ~/.ssh/id_ed25519.pub "${REMOTE_TARGET}"
  elif [ -f ~/.ssh/id_rsa.pub ]; then
    ssh-copy-id -i ~/.ssh/id_rsa.pub "${REMOTE_TARGET}"
  fi
  
  if ssh -o ConnectTimeout=10 -o BatchMode=yes "${REMOTE_TARGET}" "echo 'SSH key setup successful'" 2>/dev/null; then
    echo -e "${GREEN}[✓] SSH key authentication is now working${NC}"
  else
    echo -e "${RED}[!] SSH key setup failed${NC}"
    exit 1
  fi
fi

# Step 4: Install ALL remote dependencies FIRST
echo -e "\n${BLUE}[4/10] Installing ALL remote dependencies${NC}"

echo -e "${YELLOW}[i] Installing essential tools on remote server...${NC}"
ssh -t "${REMOTE_TARGET}" "
  echo 'Updating package lists...'
  sudo apt-get update
  
  echo 'Installing essential tools...'
  sudo apt-get install -y unzip curl wget net-tools systemctl nginx
  
  echo 'Installing Node.js if not present...'
  if ! command -v node >/dev/null 2>&1; then
    curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
    sudo apt-get install -y nodejs
  fi
  
  echo 'Verifying tools installation:'
  command -v unzip && echo '✓ unzip installed' || echo '✗ unzip failed'
  command -v curl && echo '✓ curl installed' || echo '✗ curl failed'
  command -v wget && echo '✓ wget installed' || echo '✗ wget failed'
  command -v node && echo '✓ node installed' || echo '✗ node failed'
  command -v npm && echo '✓ npm installed' || echo '✗ npm failed'
  command -v nginx && echo '✓ nginx installed' || echo '✗ nginx failed'
  command -v systemctl && echo '✓ systemctl available' || echo '✗ systemctl failed'
"

echo -e "${GREEN}[✓] All remote dependencies installed${NC}"

# Step 5: Build application locally
echo -e "\n${BLUE}[5/10] Building application locally${NC}"

cd "${LOCAL_PROJECT_DIR}"

echo -e "${YELLOW}[i] Installing dependencies...${NC}"
npm install

echo -e "${YELLOW}[i] Building for production...${NC}"
npm run build

if [ ! -d "dist" ]; then
  echo -e "${RED}[!] Build failed - dist directory not created${NC}"
  exit 1
fi

echo -e "${YELLOW}[i] Build output:${NC}"
ls -la dist/

echo -e "${GREEN}[✓] Application built successfully${NC}"

# Step 6: Create deployment package
echo -e "\n${BLUE}[6/10] Creating deployment package${NC}"

DEPLOY_PACKAGE="/tmp/focusbudget-deploy-$(date +%Y%m%d-%H%M%S).zip"
TEMP_PACKAGE_DIR="/tmp/focusbudget-package-$$"
mkdir -p "$TEMP_PACKAGE_DIR"

echo -e "${YELLOW}[i] Copying files to package directory...${NC}"
cp -r dist/ "$TEMP_PACKAGE_DIR/"
cp server.cjs "$TEMP_PACKAGE_DIR/"
cp package.json "$TEMP_PACKAGE_DIR/"
cp package-lock.json "$TEMP_PACKAGE_DIR/" 2>/dev/null || true
cp -r src/ "$TEMP_PACKAGE_DIR/"

echo -e "${YELLOW}[i] Package contents:${NC}"
ls -la "$TEMP_PACKAGE_DIR/"

# Verify essential files
for file in package.json server.cjs; do
  if [ ! -f "$TEMP_PACKAGE_DIR/$file" ]; then
    echo -e "${RED}[!] $file missing from package${NC}"
    exit 1
  fi
done

if [ ! -d "$TEMP_PACKAGE_DIR/dist" ]; then
  echo -e "${RED}[!] dist directory missing from package${NC}"
  exit 1
fi

cd "$TEMP_PACKAGE_DIR"
echo -e "${YELLOW}[i] Creating zip file...${NC}"
zip -r "$DEPLOY_PACKAGE" .
cd - > /dev/null

echo -e "${YELLOW}[i] Verifying zip file contents:${NC}"
unzip -l "$DEPLOY_PACKAGE"

rm -rf "$TEMP_PACKAGE_DIR"
echo -e "${GREEN}[✓] Deployment package created: ${DEPLOY_PACKAGE}${NC}"

# Step 7: Setup remote directories and transfer package
echo -e "\n${BLUE}[7/10] Setting up remote directories and transferring package${NC}"

REMOTE_STAGING="/tmp/focusbudget-staging-$(date +%Y%m%d-%H%M%S)"
REMOTE_BACKUP="/tmp/focusbudget-backup-$(date +%Y%m%d-%H%M%S)"

ssh "${REMOTE_TARGET}" "
  echo 'Creating staging directory: ${REMOTE_STAGING}'
  mkdir -p '${REMOTE_STAGING}'
  
  if [ -d '${APP_ROOT}' ]; then
    echo 'Creating backup of existing deployment...'
    sudo mkdir -p '${REMOTE_BACKUP}'
    sudo cp -r '${APP_ROOT}' '${REMOTE_BACKUP}/app' 2>/dev/null || true
    sudo cp -r '${DATA_ROOT}' '${REMOTE_BACKUP}/data' 2>/dev/null || true
    echo 'Backup created at: ${REMOTE_BACKUP}'
  fi
"

echo -e "${YELLOW}[i] Uploading deployment package...${NC}"
scp "$DEPLOY_PACKAGE" "${REMOTE_TARGET}:${REMOTE_STAGING}/package.zip"

echo -e "${YELLOW}[i] Extracting package on remote server...${NC}"
ssh "${REMOTE_TARGET}" "
  cd '${REMOTE_STAGING}'
  echo 'Extracting package.zip...'
  unzip -o package.zip
  rm package.zip
  
  echo 'Verifying extraction:'
  ls -la
  
  for file in package.json server.cjs; do
    if [ -f \"\$file\" ]; then
      echo \"✓ \$file found\"
    else
      echo \"✗ \$file missing\"
      exit 1
    fi
  done
"

rm -f "$DEPLOY_PACKAGE"
echo -e "${GREEN}[✓] Package transferred and extracted successfully${NC}"

# Step 8: Setup service account and install application
echo -e "\n${BLUE}[8/10] Setting up service account and installing application${NC}"

ssh -t "${REMOTE_TARGET}" "
  echo 'Setting up service account and application...'

  # Create group if it doesn't exist
  if ! getent group '${APP_GROUP}' >/dev/null 2>&1; then
    echo 'Creating group: ${APP_GROUP}'
    sudo groupadd --system '${APP_GROUP}'
  fi

  # Create user if it doesn't exist
  if ! getent passwd '${APP_USER}' >/dev/null 2>&1; then
    echo 'Creating user: ${APP_USER}'
    sudo useradd --system --gid '${APP_GROUP}' --home-dir '${APP_ROOT}' \
      --shell /bin/false --comment 'FocusBudget Application User' '${APP_USER}'
  fi

  # Create application directories
  sudo mkdir -p '${APP_ROOT}'
  sudo mkdir -p '${DATA_ROOT}'
  sudo mkdir -p '${DATA_ROOT}/history'
  sudo mkdir -p '${LOG_ROOT}'

  # Stop existing service if running
  sudo systemctl stop ${APP_NAME} 2>/dev/null || true

  # Install application files
  echo 'Installing application files...'
  sudo cp -r '${REMOTE_STAGING}'/* '${APP_ROOT}'/

  # Install Node.js dependencies
  cd '${APP_ROOT}'
  sudo npm install --omit=dev

  # Set proper ownership and permissions
  sudo chown -R '${APP_USER}:${APP_GROUP}' '${APP_ROOT}'
  sudo chown -R '${APP_USER}:${APP_GROUP}' '${DATA_ROOT}'
  sudo chown -R '${APP_USER}:${APP_GROUP}' '${LOG_ROOT}'

  sudo chmod 755 '${APP_ROOT}'
  sudo chmod 644 '${APP_ROOT}'/*.json 2>/dev/null || true
  sudo chmod 644 '${APP_ROOT}'/*.cjs 2>/dev/null || true
  sudo chmod -R 755 '${APP_ROOT}/dist' 2>/dev/null || true
  sudo chmod -R 755 '${APP_ROOT}/node_modules' 2>/dev/null || true
  sudo chmod 750 '${DATA_ROOT}'
  sudo chmod 750 '${LOG_ROOT}'

  # Create environment file
  sudo tee '${APP_ROOT}/.env' > /dev/null << ENV_EOF
NODE_ENV=production
PORT=3001
SERVER_PORT=3001
DATA_DIR=${DATA_ROOT}
LOG_DIR=${LOG_ROOT}
ENV_EOF

  sudo chown '${APP_USER}:${APP_GROUP}' '${APP_ROOT}/.env'
  sudo chmod 640 '${APP_ROOT}/.env'

  echo 'Service account and application setup completed!'
"

echo -e "${GREEN}[✓] Service account and application installed${NC}"

# Step 9: Setup systemd service and Nginx
echo -e "\n${BLUE}[9/10] Setting up systemd service and Nginx${NC}"

ssh -t "${REMOTE_TARGET}" "
  echo 'Creating systemd service...'

  sudo tee /etc/systemd/system/${APP_NAME}.service > /dev/null << SERVICE_EOF
[Unit]
Description=FocusBudget Personal Budget Management Application
After=network.target
Wants=network-online.target

[Service]
Type=simple
User=${APP_USER}
Group=${APP_GROUP}
WorkingDirectory=${APP_ROOT}
ExecStart=/usr/bin/node server.cjs
Restart=on-failure
RestartSec=10
TimeoutStartSec=30
TimeoutStopSec=30

Environment=NODE_ENV=production
Environment=PORT=3001
Environment=DATA_DIR=${DATA_ROOT}
Environment=LOG_DIR=${LOG_ROOT}
EnvironmentFile=${APP_ROOT}/.env

# Security settings
NoNewPrivileges=true
PrivateTmp=true
PrivateDevices=true
ProtectHome=true
ProtectSystem=strict
ReadWritePaths=${DATA_ROOT}
ReadWritePaths=${LOG_ROOT}
ReadOnlyPaths=${APP_ROOT}

[Install]
WantedBy=multi-user.target
SERVICE_EOF

  echo 'Configuring Nginx...'

  sudo tee /etc/nginx/sites-available/${APP_NAME} > /dev/null << NGINX_EOF
server {
    listen 80;
    listen [::]:80;
    server_name _;

    # Security headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection \"1; mode=block\" always;
    add_header Referrer-Policy strict-origin-when-cross-origin always;
    server_tokens off;

    # Increase client max body size for CSV uploads
    client_max_body_size 10M;

    # Main application proxy
    location / {
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \\\$http_upgrade;
        proxy_set_header Connection \"upgrade\";
        proxy_set_header Host \\\$host;
        proxy_set_header X-Real-IP \\\$remote_addr;
        proxy_set_header X-Forwarded-For \\\$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \\\$scheme;
        proxy_cache_bypass \\\$http_upgrade;

        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # API endpoints
    location /api/ {
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Host \\\$host;
        proxy_set_header X-Real-IP \\\$remote_addr;
        proxy_set_header X-Forwarded-For \\\$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \\\$scheme;

        proxy_connect_timeout 120s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;
    }

    # Health check endpoint
    location /health {
        proxy_pass http://127.0.0.1:3001/api/status;
        proxy_set_header Host \\\$host;
        access_log off;
    }
}
NGINX_EOF

  # Enable the site and disable default
  sudo mkdir -p /etc/nginx/sites-enabled
  sudo ln -sf /etc/nginx/sites-available/${APP_NAME} /etc/nginx/sites-enabled/${APP_NAME}
  sudo rm -f /etc/nginx/sites-enabled/default

  # Test Nginx configuration
  if sudo nginx -t; then
    echo 'Nginx configuration is valid'
  else
    echo 'Nginx configuration test failed'
    exit 1
  fi

  # Enable and start services
  sudo systemctl daemon-reload
  sudo systemctl enable ${APP_NAME}
  sudo systemctl enable nginx
  sudo systemctl restart nginx
  sudo systemctl start ${APP_NAME}

  echo 'Services setup completed!'
"

echo -e "${GREEN}[✓] Services configured and started${NC}"

# Step 10: Test and validate deployment
echo -e "\n${BLUE}[10/10] Testing and validating deployment${NC}"

echo -e "${YELLOW}[i] Waiting for services to start...${NC}"
sleep 5

ssh "${REMOTE_TARGET}" "
  echo 'Testing service status...'

  # Check FocusBudget service
  if systemctl is-active --quiet ${APP_NAME} 2>/dev/null; then
    echo '✓ FocusBudget service is running'
  else
    echo '✗ FocusBudget service is not running'
    systemctl status ${APP_NAME} --no-pager 2>/dev/null || echo 'Cannot check service status'
  fi

  # Check Nginx service
  if systemctl is-active --quiet nginx 2>/dev/null; then
    echo '✓ Nginx service is running'
  else
    echo '✗ Nginx service is not running'
    systemctl status nginx --no-pager 2>/dev/null || echo 'Cannot check nginx status'
  fi

  echo ''
  echo 'Testing connectivity...'

  # Test port 80 (Nginx)
  if curl -s -f http://localhost/ > /dev/null; then
    echo '✓ Port 80 (Nginx) is responding'
  else
    echo '✗ Port 80 (Nginx) is not responding'
  fi

  # Test port 3001 (direct backend)
  if curl -s -f http://localhost:3001/ > /dev/null; then
    echo '✓ Port 3001 (Backend) is responding'
  else
    echo '✗ Port 3001 (Backend) is not responding'
  fi

  # Test health endpoint
  if curl -s -f http://localhost/health > /dev/null; then
    echo '✓ Health endpoint is responding'
  else
    echo '✗ Health endpoint is not responding'
  fi

  echo ''
  echo 'Testing version endpoint...'

  # Test version endpoint and display version
  VERSION_RESPONSE=\$(curl -s http://localhost/api/version 2>/dev/null || curl -s http://localhost:3001/api/version 2>/dev/null)
  if [ -n \"\$VERSION_RESPONSE\" ]; then
    echo '✓ Version endpoint is responding'
    echo \"Version info: \$VERSION_RESPONSE\"
  else
    echo '✗ Version endpoint is not responding'
  fi

  echo ''
  echo 'Port status:'
  netstat -tln 2>/dev/null | grep -E ':(80|3001) ' || ss -tln 2>/dev/null | grep -E ':(80|3001) ' || echo 'No services found on ports 80 or 3001'
"

# Cleanup staging directory
echo -e "\n${YELLOW}[i] Cleaning up staging directory...${NC}"
ssh "${REMOTE_TARGET}" "rm -rf '${REMOTE_STAGING}'"

echo -e "\n${GREEN}==============================="
echo -e " Deployment Complete!"
echo -e "===============================${NC}"

echo -e "\n${BLUE}Deployment Summary:${NC}"
echo -e "${GREEN}[✓] Application version ${APP_VERSION} deployed successfully${NC}"
echo -e "${GREEN}[✓] ALL dependencies installed (unzip, curl, wget, node, npm, nginx)${NC}"
echo -e "${GREEN}[✓] Service user '${APP_USER}' created with restricted permissions${NC}"
echo -e "${GREEN}[✓] Application installed to ${APP_ROOT}${NC}"
echo -e "${GREEN}[✓] Data directory: ${DATA_ROOT}${NC}"
echo -e "${GREEN}[✓] Log directory: ${LOG_ROOT}${NC}"
echo -e "${GREEN}[✓] Systemd service '${APP_NAME}' configured and running${NC}"
echo -e "${GREEN}[✓] Nginx reverse proxy configured on port 80${NC}"

echo -e "\n${BLUE}Access Points:${NC}"
echo -e "${YELLOW}• Primary Application: http://${REMOTE_HOST}${NC}"
echo -e "${YELLOW}• Health Check: http://${REMOTE_HOST}/health${NC}"
echo -e "${YELLOW}• Version Info: http://${REMOTE_HOST}/api/version${NC}"
echo -e "${YELLOW}• API Status: http://${REMOTE_HOST}/api/status${NC}"

echo -e "\n${BLUE}Management Commands:${NC}"
echo -e "${YELLOW}• Service status: ssh ${REMOTE_TARGET} 'sudo systemctl status ${APP_NAME}'${NC}"
echo -e "${YELLOW}• View logs: ssh ${REMOTE_TARGET} 'sudo journalctl -u ${APP_NAME} -f'${NC}"
echo -e "${YELLOW}• Restart service: ssh ${REMOTE_TARGET} 'sudo systemctl restart ${APP_NAME}'${NC}"
echo -e "${YELLOW}• Nginx status: ssh ${REMOTE_TARGET} 'sudo systemctl status nginx'${NC}"
echo -e "${YELLOW}• Check version: curl -s http://${REMOTE_HOST}/api/version${NC}"

echo -e "\n${BLUE}Security Features:${NC}"
echo -e "${YELLOW}• Dedicated system user with no shell access${NC}"
echo -e "${YELLOW}• Restricted file permissions and ownership${NC}"
echo -e "${YELLOW}• Systemd security sandbox enabled${NC}"
echo -e "${YELLOW}• Nginx security headers configured${NC}"

if ssh "${REMOTE_TARGET}" "[ -d '${REMOTE_BACKUP}' ]" 2>/dev/null; then
  echo -e "\n${BLUE}Backup Information:${NC}"
  echo -e "${YELLOW}• Previous deployment backed up to: ${REMOTE_BACKUP}${NC}"
fi

echo -e "\n${GREEN}FocusBudget v${APP_VERSION} is now running securely on ${REMOTE_HOST}!${NC}"
echo -e "${YELLOW}You can access the application at: http://${REMOTE_HOST}${NC}"

# Final version validation
echo -e "\n${BLUE}Final Version Validation:${NC}"
DEPLOYED_VERSION=\$(curl -s \"http://${REMOTE_HOST}/api/version\" 2>/dev/null | grep -o '\"version\":\"[^\"]*\"' | cut -d'\"' -f4 2>/dev/null || echo \"unknown\")
if [ \"\$DEPLOYED_VERSION\" = \"\$APP_VERSION\" ]; then
  echo -e \"${GREEN}[✓] Deployed version matches expected version: ${APP_VERSION}${NC}\"
else
  echo -e \"${YELLOW}[!] Version check - Expected: ${APP_VERSION}, Deployed: \${DEPLOYED_VERSION}${NC}\"
fi
