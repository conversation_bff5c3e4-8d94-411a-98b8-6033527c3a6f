#!/bin/bash

# FocusBudget Simple Remote Deployment Script
# This script performs a complete redeployment using the original build-and-deploy.sh

# Exit on error
set -e

# Colors for better readability
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

# Configuration
REMOTE_HOST="**********"
REMOTE_USER="labmaster"
REMOTE_TARGET="${REMOTE_USER}@${REMOTE_HOST}"
LOCAL_PROJECT_DIR="/BudgetPage/project"
REMOTE_TEMP_DIR="/tmp/focusbudget-deployment-$(date +%Y%m%d-%H%M%S)"

echo -e "\n${BLUE}==============================="
echo -e " FocusBudget Simple Remote Deployment"
echo -e "===============================${NC}\n"

echo -e "${BLUE}Configuration:${NC}"
echo -e "${YELLOW}• Remote server: ${REMOTE_HOST}${NC}"
echo -e "${YELLOW}• Remote user: ${REMOTE_USER}${NC}"
echo -e "${YELLOW}• Local project: ${LOCAL_PROJECT_DIR}${NC}"
echo -e "${YELLOW}• Using original build-and-deploy.sh script${NC}"

# Check if we're in the right directory
if [ ! -f "${LOCAL_PROJECT_DIR}/package.json" ]; then
  echo -e "${RED}[!] package.json not found in ${LOCAL_PROJECT_DIR}${NC}"
  exit 1
fi

if [ ! -f "${LOCAL_PROJECT_DIR}/server.cjs" ]; then
  echo -e "${RED}[!] server.cjs not found in ${LOCAL_PROJECT_DIR}${NC}"
  exit 1
fi

if [ ! -f "${LOCAL_PROJECT_DIR}/build-and-deploy.sh" ]; then
  echo -e "${RED}[!] build-and-deploy.sh not found in ${LOCAL_PROJECT_DIR}${NC}"
  exit 1
fi

echo -e "${GREEN}[✓] Local project structure validated${NC}"

# Check SSH connection
echo -e "\n${BLUE}[>] Testing SSH connection${NC}"
if ! ssh -o ConnectTimeout=10 "${REMOTE_TARGET}" "echo 'SSH connection successful'" 2>/dev/null; then
  echo -e "${RED}[!] Cannot connect to ${REMOTE_TARGET}${NC}"
  echo -e "${YELLOW}[i] Please ensure SSH access is configured${NC}"
  exit 1
fi

echo -e "${GREEN}[✓] SSH connection successful${NC}"

# Create backup
echo -e "\n${BLUE}[>] Creating backup of existing deployment${NC}"
BACKUP_DIR="/tmp/focusbudget-backup-$(date +%Y%m%d-%H%M%S)"

ssh "${REMOTE_TARGET}" "
  if [ -d '/opt/focusbudget' ]; then
    echo 'Creating backup...'
    sudo mkdir -p '${BACKUP_DIR}'
    sudo cp -r /opt/focusbudget '${BACKUP_DIR}/app-backup' 2>/dev/null || true
    sudo cp -r /var/lib/focusbudget '${BACKUP_DIR}/data-backup' 2>/dev/null || true
    echo 'Backup created at: ${BACKUP_DIR}'
  else
    echo 'No existing deployment found'
  fi
"

# Stop existing services
echo -e "\n${BLUE}[>] Stopping existing services${NC}"
ssh "${REMOTE_TARGET}" "
  for service in focusbudget budgetpage; do
    if sudo systemctl is-active --quiet \$service 2>/dev/null; then
      echo 'Stopping service: '\$service
      sudo systemctl stop \$service
    fi
  done
"

# Transfer the codebase
echo -e "\n${BLUE}[>] Transferring codebase${NC}"
ssh "${REMOTE_TARGET}" "mkdir -p '${REMOTE_TEMP_DIR}'"

rsync -av --progress \
  --exclude 'node_modules' \
  --exclude '.git' \
  --exclude 'dist' \
  --exclude '*.log' \
  "${LOCAL_PROJECT_DIR}/" \
  "${REMOTE_TARGET}:${REMOTE_TEMP_DIR}/"

echo -e "${GREEN}[✓] Codebase transferred${NC}"

# Run deployment
echo -e "\n${BLUE}[>] Running deployment on remote server${NC}"
ssh "${REMOTE_TARGET}" "
  cd '${REMOTE_TEMP_DIR}'
  chmod +x build-and-deploy.sh
  
  echo 'Starting deployment...'
  echo 'Working directory: \$(pwd)'
  
  # Run the original deployment script
  sudo ./build-and-deploy.sh
  
  echo 'Deployment completed!'
"

# Verify deployment
echo -e "\n${BLUE}[>] Verifying deployment${NC}"
sleep 3

ssh "${REMOTE_TARGET}" "
  echo 'Service status:'
  sudo systemctl status focusbudget --no-pager || true
  
  echo ''
  echo 'Testing connectivity:'
  curl -s -I http://localhost/ | head -3 || echo 'Port 80 test failed'
  curl -s http://localhost/health || curl -s http://localhost:3001/api/status || echo 'Health check failed'
"

# Cleanup
echo -e "\n${BLUE}[>] Cleaning up${NC}"
ssh "${REMOTE_TARGET}" "rm -rf '${REMOTE_TEMP_DIR}'"

echo -e "\n${GREEN}==============================="
echo -e " Deployment Complete!"
echo -e "===============================${NC}"

echo -e "\n${BLUE}Access Points:${NC}"
echo -e "${YELLOW}• Primary: http://${REMOTE_HOST}${NC}"
echo -e "${YELLOW}• Health: http://${REMOTE_HOST}/health${NC}"
echo -e "${YELLOW}• Direct backend: http://${REMOTE_HOST}:3001${NC}"

echo -e "\n${BLUE}Management:${NC}"
echo -e "${YELLOW}• Service status: ssh ${REMOTE_TARGET} 'sudo systemctl status focusbudget'${NC}"
echo -e "${YELLOW}• View logs: ssh ${REMOTE_TARGET} 'sudo journalctl -u focusbudget -f'${NC}"
echo -e "${YELLOW}• Restart: ssh ${REMOTE_TARGET} 'sudo systemctl restart focusbudget'${NC}"

echo -e "\n${BLUE}Backup:${NC}"
echo -e "${YELLOW}• Previous deployment backed up to: ${BACKUP_DIR}${NC}"

echo -e "\n${GREEN}FocusBudget deployment completed successfully!${NC}"
