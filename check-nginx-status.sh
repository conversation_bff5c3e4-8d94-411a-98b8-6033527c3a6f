#!/bin/bash

# FocusBudget Nginx Diagnostic Script
# This script checks the current Nginx configuration and status on the remote server

# Colors for better readability
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

# Configuration
REMOTE_HOST="**********"
REMOTE_USER="labmaster"
REMOTE_TARGET="${REMOTE_USER}@${REMOTE_HOST}"

echo -e "\n${BLUE}==============================="
echo -e " FocusBudget Nginx Diagnostics"
echo -e "===============================${NC}\n"

# Function to execute SSH commands (assumes SSH is already set up)
ssh_exec() {
  ssh "$REMOTE_TARGET" "$@"
}

echo -e "${BLUE}[>] Checking Nginx installation and status${NC}"

# Check if Nginx is installed
echo -e "\n${YELLOW}1. Nginx Installation:${NC}"
ssh_exec "
  if command -v nginx >/dev/null 2>&1; then
    echo '✓ Nginx is installed'
    nginx -v
  else
    echo '✗ Nginx is NOT installed'
  fi
"

# Check if Nginx is running
echo -e "\n${YELLOW}2. Nginx Service Status:${NC}"
ssh_exec "
  if systemctl is-active --quiet nginx; then
    echo '✓ Nginx service is running'
    sudo systemctl status nginx --no-pager -l
  else
    echo '✗ Nginx service is NOT running'
    sudo systemctl status nginx --no-pager -l || true
  fi
"

# Check Nginx configuration
echo -e "\n${YELLOW}3. Nginx Configuration:${NC}"
ssh_exec "
  echo 'Testing Nginx configuration:'
  sudo nginx -t
  
  echo ''
  echo 'Main Nginx config:'
  if [ -f /etc/nginx/nginx.conf ]; then
    echo '✓ /etc/nginx/nginx.conf exists'
  else
    echo '✗ /etc/nginx/nginx.conf missing'
  fi
"

# Check sites-available and sites-enabled
echo -e "\n${YELLOW}4. Sites Configuration:${NC}"
ssh_exec "
  echo 'Sites available:'
  if [ -d /etc/nginx/sites-available ]; then
    ls -la /etc/nginx/sites-available/
  else
    echo '✗ /etc/nginx/sites-available directory does not exist'
  fi
  
  echo ''
  echo 'Sites enabled:'
  if [ -d /etc/nginx/sites-enabled ]; then
    ls -la /etc/nginx/sites-enabled/
  else
    echo '✗ /etc/nginx/sites-enabled directory does not exist'
  fi
"

# Check FocusBudget specific configuration
echo -e "\n${YELLOW}5. FocusBudget Nginx Configuration:${NC}"
ssh_exec "
  if [ -f /etc/nginx/sites-available/focusbudget ]; then
    echo '✓ FocusBudget config exists in sites-available'
    echo 'Content:'
    sudo cat /etc/nginx/sites-available/focusbudget
  else
    echo '✗ FocusBudget config missing from sites-available'
  fi
  
  echo ''
  if [ -L /etc/nginx/sites-enabled/focusbudget ]; then
    echo '✓ FocusBudget config is enabled (symlinked)'
    ls -la /etc/nginx/sites-enabled/focusbudget
  else
    echo '✗ FocusBudget config is NOT enabled'
  fi
"

# Check default site
echo -e "\n${YELLOW}6. Default Site Configuration:${NC}"
ssh_exec "
  if [ -f /etc/nginx/sites-enabled/default ]; then
    echo '⚠ Default Nginx site is still enabled (may conflict)'
    echo 'Default site config:'
    sudo head -20 /etc/nginx/sites-enabled/default
  else
    echo '✓ Default site is disabled'
  fi
"

# Check port 80 binding
echo -e "\n${YELLOW}7. Port 80 Status:${NC}"
ssh_exec "
  echo 'Processes listening on port 80:'
  sudo netstat -tlnp | grep ':80 ' || echo 'No processes listening on port 80'
  
  echo ''
  echo 'All listening ports:'
  sudo netstat -tlnp | grep LISTEN
"

# Test connectivity
echo -e "\n${YELLOW}8. Connectivity Tests:${NC}"
ssh_exec "
  echo 'Testing localhost:80:'
  curl -s -I http://localhost/ | head -5 || echo 'Failed to connect to port 80'
  
  echo ''
  echo 'Testing localhost:3001 (direct backend):'
  curl -s -I http://localhost:3001/ | head -5 || echo 'Failed to connect to port 3001'
  
  echo ''
  echo 'Testing health endpoint:'
  curl -s http://localhost/health || curl -s http://localhost:3001/api/status || echo 'Health check failed'
"

# Check FocusBudget service
echo -e "\n${YELLOW}9. FocusBudget Service Status:${NC}"
ssh_exec "
  if systemctl is-active --quiet focusbudget; then
    echo '✓ FocusBudget service is running'
    sudo systemctl status focusbudget --no-pager -l
  else
    echo '✗ FocusBudget service is NOT running'
    sudo systemctl status focusbudget --no-pager -l || true
  fi
"

# Check recent logs
echo -e "\n${YELLOW}10. Recent Logs:${NC}"
ssh_exec "
  echo 'Recent Nginx error logs:'
  sudo tail -10 /var/log/nginx/error.log 2>/dev/null || echo 'No Nginx error logs found'
  
  echo ''
  echo 'Recent FocusBudget logs:'
  sudo journalctl -u focusbudget --since '10 minutes ago' --no-pager || echo 'No FocusBudget logs found'
"

echo -e "\n${BLUE}==============================="
echo -e " Diagnostic Complete"
echo -e "===============================${NC}"

echo -e "\n${YELLOW}Quick Fix Commands (if needed):${NC}"
echo -e "${YELLOW}• Install Nginx: ssh $REMOTE_TARGET 'sudo apt-get update && sudo apt-get install -y nginx'${NC}"
echo -e "${YELLOW}• Start Nginx: ssh $REMOTE_TARGET 'sudo systemctl start nginx'${NC}"
echo -e "${YELLOW}• Enable Nginx: ssh $REMOTE_TARGET 'sudo systemctl enable nginx'${NC}"
echo -e "${YELLOW}• Restart Nginx: ssh $REMOTE_TARGET 'sudo systemctl restart nginx'${NC}"
echo -e "${YELLOW}• Remove default site: ssh $REMOTE_TARGET 'sudo rm -f /etc/nginx/sites-enabled/default'${NC}"
echo -e "${YELLOW}• Test config: ssh $REMOTE_TARGET 'sudo nginx -t'${NC}"
