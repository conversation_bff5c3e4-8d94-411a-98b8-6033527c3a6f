#!/bin/bash

# FocusBudget Remote Deployment Script
# This script performs a complete redeployment to the remote server

# Exit on error
set -e

# Colors for better readability
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

# Configuration
REMOTE_HOST="**********"
REMOTE_USER="labmaster"
REMOTE_TARGET="${REMOTE_USER}@${REMOTE_HOST}"
LOCAL_PROJECT_DIR="/BudgetPage/project"
REMOTE_TEMP_DIR="/tmp/focusbudget-deployment-$(date +%Y%m%d-%H%M%S)"
SERVICE_NAME="focusbudget"

echo -e "\n${BLUE}==============================="
echo -e " FocusBudget Remote Deployment"
echo -e "===============================${NC}\n"

echo -e "${BLUE}Configuration:${NC}"
echo -e "${YELLOW}• Remote server: ${REMOTE_HOST}${NC}"
echo -e "${YELLOW}• Remote user: ${REMOTE_USER}${NC}"
echo -e "${YELLOW}• Local project: ${LOCAL_PROJECT_DIR}${NC}"
echo -e "${YELLOW}• Remote temp dir: ${REMOTE_TEMP_DIR}${NC}"

# Check if we're in the right directory
if [ ! -f "${LOCAL_PROJECT_DIR}/package.json" ]; then
  echo -e "${RED}[!] package.json not found in ${LOCAL_PROJECT_DIR}${NC}"
  echo -e "${YELLOW}[i] Please ensure the project directory is correct${NC}"
  exit 1
fi

if [ ! -f "${LOCAL_PROJECT_DIR}/server.cjs" ]; then
  echo -e "${RED}[!] server.cjs not found in ${LOCAL_PROJECT_DIR}${NC}"
  exit 1
fi

if [ ! -f "${LOCAL_PROJECT_DIR}/build-and-deploy-secure.sh" ]; then
  echo -e "${RED}[!] build-and-deploy-secure.sh not found in ${LOCAL_PROJECT_DIR}${NC}"
  echo -e "${YELLOW}[i] This script is required for secure deployment${NC}"
  exit 1
fi

echo -e "${GREEN}[✓] Local project structure validated${NC}"

# Check for SSH authentication method
echo -e "\n${BLUE}[>] Checking SSH authentication${NC}"

# First, check if we can connect with key-based auth
if ssh -o ConnectTimeout=10 -o BatchMode=yes "${REMOTE_TARGET}" "echo 'SSH key authentication successful'" 2>/dev/null; then
  echo -e "${GREEN}[✓] SSH key authentication is working${NC}"
  SSH_METHOD="key"
else
  echo -e "${YELLOW}[!] SSH key authentication not available${NC}"
  echo -e "\n${BLUE}Choose authentication method:${NC}"
  echo -e "${YELLOW}1. Set up SSH key authentication (recommended)${NC}"
  echo -e "${YELLOW}2. Use password authentication${NC}"
  echo -e "${YELLOW}3. Exit and set up manually${NC}"

  read -p "Enter your choice (1-3): " auth_choice

  case $auth_choice in
    1)
      echo -e "\n${BLUE}[>] Setting up SSH key authentication${NC}"

      # Check if SSH key exists
      if [ ! -f ~/.ssh/id_rsa.pub ] && [ ! -f ~/.ssh/id_ed25519.pub ]; then
        echo -e "${YELLOW}[i] No SSH key found. Generating new SSH key...${NC}"
        read -p "Enter your email for SSH key: " user_email
        ssh-keygen -t ed25519 -C "$user_email" -f ~/.ssh/id_ed25519 -N ""
        echo -e "${GREEN}[✓] SSH key generated${NC}"
      fi

      # Copy SSH key to remote server
      echo -e "${YELLOW}[i] Copying SSH key to remote server...${NC}"
      echo -e "${YELLOW}[i] You will be prompted for the password for ${REMOTE_TARGET}${NC}"

      if [ -f ~/.ssh/id_ed25519.pub ]; then
        ssh-copy-id -i ~/.ssh/id_ed25519.pub "${REMOTE_TARGET}"
      elif [ -f ~/.ssh/id_rsa.pub ]; then
        ssh-copy-id -i ~/.ssh/id_rsa.pub "${REMOTE_TARGET}"
      else
        echo -e "${RED}[!] No SSH public key found${NC}"
        exit 1
      fi

      # Test the connection again
      if ssh -o ConnectTimeout=10 -o BatchMode=yes "${REMOTE_TARGET}" "echo 'SSH key setup successful'" 2>/dev/null; then
        echo -e "${GREEN}[✓] SSH key authentication is now working${NC}"
        SSH_METHOD="key"
      else
        echo -e "${RED}[!] SSH key setup failed${NC}"
        exit 1
      fi
      ;;
    2)
      echo -e "\n${YELLOW}[i] Using password authentication${NC}"
      echo -e "${YELLOW}[i] You will be prompted for passwords during deployment${NC}"

      # Check if sshpass is available
      if ! command -v sshpass >/dev/null 2>&1; then
        echo -e "${YELLOW}[i] Installing sshpass for password authentication...${NC}"
        if command -v apt-get >/dev/null 2>&1; then
          sudo apt-get update && sudo apt-get install -y sshpass
        elif command -v yum >/dev/null 2>&1; then
          sudo yum install -y sshpass
        elif command -v dnf >/dev/null 2>&1; then
          sudo dnf install -y sshpass
        else
          echo -e "${RED}[!] Cannot install sshpass automatically${NC}"
          echo -e "${YELLOW}[i] Please install sshpass manually or use SSH keys${NC}"
          exit 1
        fi
      fi

      # Test password authentication
      read -s -p "Enter password for ${REMOTE_TARGET}: " REMOTE_PASSWORD
      echo

      if echo "$REMOTE_PASSWORD" | sshpass -p "$REMOTE_PASSWORD" ssh -o ConnectTimeout=10 "${REMOTE_TARGET}" "echo 'Password authentication successful'" 2>/dev/null; then
        echo -e "${GREEN}[✓] Password authentication is working${NC}"
        SSH_METHOD="password"
      else
        echo -e "${RED}[!] Password authentication failed${NC}"
        exit 1
      fi
      ;;
    3)
      echo -e "\n${YELLOW}[i] Manual SSH setup instructions:${NC}"
      echo -e "${YELLOW}1. Generate SSH key: ssh-keygen -t ed25519 -C '<EMAIL>'${NC}"
      echo -e "${YELLOW}2. Copy key to server: ssh-copy-id ${REMOTE_TARGET}${NC}"
      echo -e "${YELLOW}3. Test connection: ssh ${REMOTE_TARGET}${NC}"
      echo -e "${YELLOW}4. Run this script again${NC}"
      exit 0
      ;;
    *)
      echo -e "${RED}[!] Invalid choice${NC}"
      exit 1
      ;;
  esac
fi

# Function to execute SSH commands based on authentication method
ssh_exec() {
  if [ "$SSH_METHOD" = "password" ]; then
    sshpass -p "$REMOTE_PASSWORD" ssh "$@"
  else
    ssh "$@"
  fi
}

# Function to execute SCP/rsync with authentication
rsync_exec() {
  if [ "$SSH_METHOD" = "password" ]; then
    sshpass -p "$REMOTE_PASSWORD" rsync "$@"
  else
    rsync "$@"
  fi
}

# Check if remote server has required tools
echo -e "\n${BLUE}[>] Checking remote server requirements${NC}"
ssh_exec "${REMOTE_TARGET}" "command -v sudo >/dev/null 2>&1" || {
  echo -e "${RED}[!] sudo not available on remote server${NC}"
  exit 1
}

echo -e "${GREEN}[✓] Remote server requirements met${NC}"

# Create backup of existing deployment if it exists
echo -e "\n${BLUE}[>] Creating backup of existing deployment${NC}"
BACKUP_DIR="/tmp/focusbudget-backup-$(date +%Y%m%d-%H%M%S)"

ssh_exec "${REMOTE_TARGET}" "
  if [ -d '/opt/focusbudget' ]; then
    echo 'Creating backup of existing deployment...'
    sudo mkdir -p '${BACKUP_DIR}'
    sudo cp -r /opt/focusbudget/* '${BACKUP_DIR}/' 2>/dev/null || true
    sudo cp -r /var/lib/focusbudget '${BACKUP_DIR}/data-backup' 2>/dev/null || true
    echo 'Backup created at: ${BACKUP_DIR}'
  else
    echo 'No existing deployment found - fresh installation'
  fi
"

echo -e "${GREEN}[✓] Backup completed${NC}"

# Stop existing service if running
echo -e "\n${BLUE}[>] Stopping existing services${NC}"
ssh_exec "${REMOTE_TARGET}" "
  for service in focusbudget budgetpage; do
    if sudo systemctl is-active --quiet \$service 2>/dev/null; then
      echo 'Stopping service: '\$service
      sudo systemctl stop \$service
    fi
  done
"

echo -e "${GREEN}[✓] Services stopped${NC}"

# Transfer the codebase
echo -e "\n${BLUE}[>] Transferring codebase to remote server${NC}"
echo -e "${YELLOW}[i] Creating remote temporary directory${NC}"
ssh_exec "${REMOTE_TARGET}" "mkdir -p '${REMOTE_TEMP_DIR}'"

echo -e "${YELLOW}[i] Syncing project files (excluding node_modules)${NC}"
if [ "$SSH_METHOD" = "password" ]; then
  # For password auth, we need to use sshpass with rsync
  rsync_exec -av --progress \
    --exclude 'node_modules' \
    --exclude '.git' \
    --exclude 'dist' \
    --exclude '*.log' \
    --exclude '.env.local' \
    -e "sshpass -p '$REMOTE_PASSWORD' ssh" \
    "${LOCAL_PROJECT_DIR}/" \
    "${REMOTE_TARGET}:${REMOTE_TEMP_DIR}/"
else
  # For key auth, use regular rsync
  rsync -av --progress \
    --exclude 'node_modules' \
    --exclude '.git' \
    --exclude 'dist' \
    --exclude '*.log' \
    --exclude '.env.local' \
    "${LOCAL_PROJECT_DIR}/" \
    "${REMOTE_TARGET}:${REMOTE_TEMP_DIR}/"
fi

echo -e "${GREEN}[✓] Codebase transferred successfully${NC}"

# Make deployment script executable
echo -e "\n${BLUE}[>] Preparing deployment script${NC}"
ssh_exec "${REMOTE_TARGET}" "chmod +x '${REMOTE_TEMP_DIR}/build-and-deploy-secure.sh'"

echo -e "${GREEN}[✓] Deployment script prepared${NC}"

# Run the secure deployment script on remote server
echo -e "\n${BLUE}[>] Running secure deployment on remote server${NC}"
echo -e "${YELLOW}[i] This will install dependencies, build the app, and configure the service${NC}"

# Create a script to run the deployment with proper environment
ssh_exec "${REMOTE_TARGET}" "
cat > '${REMOTE_TEMP_DIR}/run-deployment.sh' << 'EOF'
#!/bin/bash
set -e

cd '${REMOTE_TEMP_DIR}'

echo 'Starting secure deployment...'
echo 'Working directory: \$(pwd)'
echo 'Contents:'
ls -la

# Run the deployment script
sudo ./build-and-deploy-secure.sh

echo 'Deployment completed successfully!'
EOF

chmod +x '${REMOTE_TEMP_DIR}/run-deployment.sh'
"

# Execute the deployment
echo -e "${YELLOW}[i] Executing deployment (this may take several minutes)...${NC}"
ssh_exec "${REMOTE_TARGET}" "'${REMOTE_TEMP_DIR}/run-deployment.sh'"

# Verify deployment
echo -e "\n${BLUE}[>] Verifying deployment${NC}"

# Check service status
echo -e "${YELLOW}[i] Checking service status${NC}"
ssh_exec "${REMOTE_TARGET}" "
  echo 'Service status:'
  sudo systemctl status ${SERVICE_NAME} --no-pager || true

  echo ''
  echo 'Recent logs:'
  sudo journalctl -u ${SERVICE_NAME} --since '2 minutes ago' --no-pager || true
"

# Test application endpoints
echo -e "\n${YELLOW}[i] Testing application endpoints${NC}"
sleep 5  # Give the service time to start

ssh_exec "${REMOTE_TARGET}" "
  echo 'Testing health endpoint:'
  curl -s http://localhost/health || curl -s http://localhost:3001/api/status || echo 'Health check failed'

  echo ''
  echo 'Testing status endpoint:'
  curl -s http://localhost/api/status || curl -s http://localhost:3001/api/status || echo 'Status check failed'
"

# Clean up temporary directory
echo -e "\n${BLUE}[>] Cleaning up${NC}"
ssh_exec "${REMOTE_TARGET}" "rm -rf '${REMOTE_TEMP_DIR}'"

echo -e "${GREEN}[✓] Cleanup completed${NC}"

# Final status report
echo -e "\n${GREEN}==============================="
echo -e " Deployment Complete!"
echo -e "===============================${NC}"

echo -e "\n${BLUE}Deployment Summary:${NC}"
echo -e "${GREEN}[✓] Codebase transferred to ${REMOTE_HOST}${NC}"
echo -e "${GREEN}[✓] Secure deployment executed${NC}"
echo -e "${GREEN}[✓] Service configured and started${NC}"
echo -e "${GREEN}[✓] Application deployed to /opt/focusbudget${NC}"
echo -e "${GREEN}[✓] Data directory: /var/lib/focusbudget${NC}"

echo -e "\n${BLUE}Access Points:${NC}"
echo -e "${YELLOW}• Primary: http://${REMOTE_HOST}${NC}"
echo -e "${YELLOW}• Health: http://${REMOTE_HOST}/health${NC}"
echo -e "${YELLOW}• API: http://${REMOTE_HOST}/api/status${NC}"

echo -e "\n${BLUE}Remote Management:${NC}"
echo -e "${YELLOW}• SSH: ssh ${REMOTE_TARGET}${NC}"
echo -e "${YELLOW}• Service status: ssh ${REMOTE_TARGET} 'sudo systemctl status ${SERVICE_NAME}'${NC}"
echo -e "${YELLOW}• View logs: ssh ${REMOTE_TARGET} 'sudo journalctl -u ${SERVICE_NAME} -f'${NC}"
echo -e "${YELLOW}• Restart service: ssh ${REMOTE_TARGET} 'sudo systemctl restart ${SERVICE_NAME}'${NC}"

echo -e "\n${BLUE}Backup Location:${NC}"
echo -e "${YELLOW}• Previous deployment backed up to: ${BACKUP_DIR}${NC}"

echo -e "\n${GREEN}FocusBudget has been successfully deployed to ${REMOTE_HOST}!${NC}"
echo -e "${YELLOW}The application should now be accessible and the route order issue should be resolved.${NC}"
