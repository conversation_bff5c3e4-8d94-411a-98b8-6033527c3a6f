#!/bin/bash

# FocusBudget Remote Deployment Script
# This script performs a complete redeployment to the remote server

# Exit on error
set -e

# Colors for better readability
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

# Configuration
REMOTE_HOST="**********"
REMOTE_USER="labmaster"
REMOTE_TARGET="${REMOTE_USER}@${REMOTE_HOST}"
LOCAL_PROJECT_DIR="/BudgetPage/project"
REMOTE_TEMP_DIR="/tmp/focusbudget-deployment-$(date +%Y%m%d-%H%M%S)"
SERVICE_NAME="focusbudget"

echo -e "\n${BLUE}==============================="
echo -e " FocusBudget Remote Deployment"
echo -e "===============================${NC}\n"

echo -e "${BLUE}Configuration:${NC}"
echo -e "${YELLOW}• Remote server: ${REMOTE_HOST}${NC}"
echo -e "${YELLOW}• Remote user: ${REMOTE_USER}${NC}"
echo -e "${YELLOW}• Local project: ${LOCAL_PROJECT_DIR}${NC}"
echo -e "${YELLOW}• Remote temp dir: ${REMOTE_TEMP_DIR}${NC}"

# Check if we're in the right directory
if [ ! -f "${LOCAL_PROJECT_DIR}/package.json" ]; then
  echo -e "${RED}[!] package.json not found in ${LOCAL_PROJECT_DIR}${NC}"
  echo -e "${YELLOW}[i] Please ensure the project directory is correct${NC}"
  exit 1
fi

if [ ! -f "${LOCAL_PROJECT_DIR}/server.cjs" ]; then
  echo -e "${RED}[!] server.cjs not found in ${LOCAL_PROJECT_DIR}${NC}"
  exit 1
fi

if [ ! -f "${LOCAL_PROJECT_DIR}/build-and-deploy-secure.sh" ]; then
  echo -e "${RED}[!] build-and-deploy-secure.sh not found in ${LOCAL_PROJECT_DIR}${NC}"
  echo -e "${YELLOW}[i] This script is required for secure deployment${NC}"
  exit 1
fi

echo -e "${GREEN}[✓] Local project structure validated${NC}"

# Test SSH connection
echo -e "\n${BLUE}[>] Testing SSH connection to ${REMOTE_TARGET}${NC}"
if ! ssh -o ConnectTimeout=10 -o BatchMode=yes "${REMOTE_TARGET}" "echo 'SSH connection successful'" 2>/dev/null; then
  echo -e "${RED}[!] Cannot connect to ${REMOTE_TARGET}${NC}"
  echo -e "${YELLOW}[i] Please ensure:${NC}"
  echo -e "${YELLOW}  • SSH key authentication is set up${NC}"
  echo -e "${YELLOW}  • The remote server is accessible${NC}"
  echo -e "${YELLOW}  • The user ${REMOTE_USER} exists on the remote server${NC}"
  echo -e "\n${YELLOW}[i] To set up SSH key authentication:${NC}"
  echo -e "${YELLOW}  ssh-copy-id ${REMOTE_TARGET}${NC}"
  exit 1
fi

echo -e "${GREEN}[✓] SSH connection successful${NC}"

# Check if remote server has required tools
echo -e "\n${BLUE}[>] Checking remote server requirements${NC}"
ssh "${REMOTE_TARGET}" "command -v sudo >/dev/null 2>&1" || {
  echo -e "${RED}[!] sudo not available on remote server${NC}"
  exit 1
}

echo -e "${GREEN}[✓] Remote server requirements met${NC}"

# Create backup of existing deployment if it exists
echo -e "\n${BLUE}[>] Creating backup of existing deployment${NC}"
BACKUP_DIR="/tmp/focusbudget-backup-$(date +%Y%m%d-%H%M%S)"

ssh "${REMOTE_TARGET}" "
  if [ -d '/opt/focusbudget' ]; then
    echo 'Creating backup of existing deployment...'
    sudo mkdir -p '${BACKUP_DIR}'
    sudo cp -r /opt/focusbudget/* '${BACKUP_DIR}/' 2>/dev/null || true
    sudo cp -r /var/lib/focusbudget '${BACKUP_DIR}/data-backup' 2>/dev/null || true
    echo 'Backup created at: ${BACKUP_DIR}'
  else
    echo 'No existing deployment found - fresh installation'
  fi
"

echo -e "${GREEN}[✓] Backup completed${NC}"

# Stop existing service if running
echo -e "\n${BLUE}[>] Stopping existing services${NC}"
ssh "${REMOTE_TARGET}" "
  for service in focusbudget budgetpage; do
    if sudo systemctl is-active --quiet \$service 2>/dev/null; then
      echo 'Stopping service: '\$service
      sudo systemctl stop \$service
    fi
  done
"

echo -e "${GREEN}[✓] Services stopped${NC}"

# Transfer the codebase
echo -e "\n${BLUE}[>] Transferring codebase to remote server${NC}"
echo -e "${YELLOW}[i] Creating remote temporary directory${NC}"
ssh "${REMOTE_TARGET}" "mkdir -p '${REMOTE_TEMP_DIR}'"

echo -e "${YELLOW}[i] Syncing project files (excluding node_modules)${NC}"
rsync -av --progress \
  --exclude 'node_modules' \
  --exclude '.git' \
  --exclude 'dist' \
  --exclude '*.log' \
  --exclude '.env.local' \
  "${LOCAL_PROJECT_DIR}/" \
  "${REMOTE_TARGET}:${REMOTE_TEMP_DIR}/"

echo -e "${GREEN}[✓] Codebase transferred successfully${NC}"

# Make deployment script executable
echo -e "\n${BLUE}[>] Preparing deployment script${NC}"
ssh "${REMOTE_TARGET}" "chmod +x '${REMOTE_TEMP_DIR}/build-and-deploy-secure.sh'"

echo -e "${GREEN}[✓] Deployment script prepared${NC}"

# Run the secure deployment script on remote server
echo -e "\n${BLUE}[>] Running secure deployment on remote server${NC}"
echo -e "${YELLOW}[i] This will install dependencies, build the app, and configure the service${NC}"

# Create a script to run the deployment with proper environment
ssh "${REMOTE_TARGET}" "
cat > '${REMOTE_TEMP_DIR}/run-deployment.sh' << 'EOF'
#!/bin/bash
set -e

cd '${REMOTE_TEMP_DIR}'

echo 'Starting secure deployment...'
echo 'Working directory: \$(pwd)'
echo 'Contents:'
ls -la

# Run the deployment script
sudo ./build-and-deploy-secure.sh

echo 'Deployment completed successfully!'
EOF

chmod +x '${REMOTE_TEMP_DIR}/run-deployment.sh'
"

# Execute the deployment
echo -e "${YELLOW}[i] Executing deployment (this may take several minutes)...${NC}"
ssh "${REMOTE_TARGET}" "'${REMOTE_TEMP_DIR}/run-deployment.sh'"

# Verify deployment
echo -e "\n${BLUE}[>] Verifying deployment${NC}"

# Check service status
echo -e "${YELLOW}[i] Checking service status${NC}"
ssh "${REMOTE_TARGET}" "
  echo 'Service status:'
  sudo systemctl status ${SERVICE_NAME} --no-pager || true
  
  echo ''
  echo 'Recent logs:'
  sudo journalctl -u ${SERVICE_NAME} --since '2 minutes ago' --no-pager || true
"

# Test application endpoints
echo -e "\n${YELLOW}[i] Testing application endpoints${NC}"
sleep 5  # Give the service time to start

ssh "${REMOTE_TARGET}" "
  echo 'Testing health endpoint:'
  curl -s http://localhost/health || curl -s http://localhost:3001/api/status || echo 'Health check failed'
  
  echo ''
  echo 'Testing status endpoint:'
  curl -s http://localhost/api/status || curl -s http://localhost:3001/api/status || echo 'Status check failed'
"

# Clean up temporary directory
echo -e "\n${BLUE}[>] Cleaning up${NC}"
ssh "${REMOTE_TARGET}" "rm -rf '${REMOTE_TEMP_DIR}'"

echo -e "${GREEN}[✓] Cleanup completed${NC}"

# Final status report
echo -e "\n${GREEN}==============================="
echo -e " Deployment Complete!"
echo -e "===============================${NC}"

echo -e "\n${BLUE}Deployment Summary:${NC}"
echo -e "${GREEN}[✓] Codebase transferred to ${REMOTE_HOST}${NC}"
echo -e "${GREEN}[✓] Secure deployment executed${NC}"
echo -e "${GREEN}[✓] Service configured and started${NC}"
echo -e "${GREEN}[✓] Application deployed to /opt/focusbudget${NC}"
echo -e "${GREEN}[✓] Data directory: /var/lib/focusbudget${NC}"

echo -e "\n${BLUE}Access Points:${NC}"
echo -e "${YELLOW}• Primary: http://${REMOTE_HOST}${NC}"
echo -e "${YELLOW}• Health: http://${REMOTE_HOST}/health${NC}"
echo -e "${YELLOW}• API: http://${REMOTE_HOST}/api/status${NC}"

echo -e "\n${BLUE}Remote Management:${NC}"
echo -e "${YELLOW}• SSH: ssh ${REMOTE_TARGET}${NC}"
echo -e "${YELLOW}• Service status: ssh ${REMOTE_TARGET} 'sudo systemctl status ${SERVICE_NAME}'${NC}"
echo -e "${YELLOW}• View logs: ssh ${REMOTE_TARGET} 'sudo journalctl -u ${SERVICE_NAME} -f'${NC}"
echo -e "${YELLOW}• Restart service: ssh ${REMOTE_TARGET} 'sudo systemctl restart ${SERVICE_NAME}'${NC}"

echo -e "\n${BLUE}Backup Location:${NC}"
echo -e "${YELLOW}• Previous deployment backed up to: ${BACKUP_DIR}${NC}"

echo -e "\n${GREEN}FocusBudget has been successfully deployed to ${REMOTE_HOST}!${NC}"
echo -e "${YELLOW}The application should now be accessible and the route order issue should be resolved.${NC}"
