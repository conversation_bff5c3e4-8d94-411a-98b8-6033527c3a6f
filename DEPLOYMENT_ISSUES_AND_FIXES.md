# FocusBudget Deployment Issues and Fixes

## Issues Identified

### 1. **Critical: Express Route Order Problem**
**Error:** `TypeError: Missing parameter name at 1: https://git.new/pathToRegexpError`

**Root Cause:** In `server.cjs`, the API routes `/api/config/openai` and `/api/status` were defined AFTER the catch-all route `app.get('*', ...)`. This caused Express to try to match these specific routes against the catch-all pattern, leading to path-to-regexp parsing errors.

**Fix Applied:** Moved the specific API routes BEFORE the catch-all route in `server.cjs`.

### 2. **Security: Running as Root/Current User**
**Issues:**
- Application running under the current user (`labmaster`) instead of a dedicated service user
- Files stored in user's home directory with broad permissions
- No security hardening in systemd service
- Potential privilege escalation risks

**Security Risks:**
- If the application is compromised, attacker has access to the user's entire home directory
- No isolation between application and system
- Logs and data accessible to other processes running under the same user

## Solutions Implemented

### 1. **Fixed Route Order (server.cjs)**
```javascript
// BEFORE (broken):
app.get('*', (req, res) => { ... });  // Catch-all route
app.get('/api/config/openai', ...);   // Never reached!
app.get('/api/status', ...);          // Never reached!

// AFTER (fixed):
app.get('/api/config/openai', ...);   // Specific routes first
app.get('/api/status', ...);          // Specific routes first
app.get('*', (req, res) => { ... });  // Catch-all route LAST
```

### 2. **Created Secure Deployment Script (build-and-deploy-secure.sh)**

#### **User and Group Management**
- Creates dedicated system user: `focusbudget_user`
- Creates dedicated system group: `focusbudget_group`
- User has no shell access (`/bin/false`)
- User cannot login interactively

#### **Directory Structure**
```
/opt/focusbudget/          # Application files (read-only for app)
├── dist/                  # Built frontend
├── server.cjs            # Backend server
├── package.json          # Dependencies
├── node_modules/         # Node.js modules
└── .env                  # Environment config (restricted access)

/var/lib/focusbudget/     # Data directory (read-write for app)
├── transactions.json
├── budget.json
└── history/              # Monthly history files

/var/log/focusbudget/     # Log directory (write-only for app)
```

#### **File Permissions**
- Application files: `644` (read-only for app user)
- Data directory: `750` (read-write for app user only)
- Environment file: `640` (read-only, group access only)
- All owned by `focusbudget_user:focusbudget_group`

#### **Systemd Security Hardening**
```ini
[Service]
User=focusbudget_user
Group=focusbudget_group

# Security restrictions
NoNewPrivileges=true
PrivateTmp=true
PrivateDevices=true
ProtectHome=true
ProtectSystem=strict
ProtectKernelTunables=true
ProtectKernelModules=true
RestrictRealtime=true
RestrictSUIDSGID=true

# Network restrictions
RestrictAddressFamilies=AF_INET AF_INET6 AF_UNIX
IPAddressDeny=any
IPAddressAllow=localhost

# File system access
ReadWritePaths=/var/lib/focusbudget
ReadWritePaths=/var/log/focusbudget
ReadOnlyPaths=/opt/focusbudget
```

#### **Nginx Security Enhancements**
- Rate limiting on API endpoints
- Security headers (XSS protection, content type sniffing prevention)
- Content Security Policy
- Server token hiding
- Restricted proxy access to localhost only

## Migration Instructions

### **Option 1: Quick Fix (Current Setup)**
If you want to keep your current setup but fix the immediate error:

1. **Stop the service:**
   ```bash
   sudo systemctl stop focusbudget
   ```

2. **The route order fix has already been applied to server.cjs**

3. **Restart the service:**
   ```bash
   sudo systemctl start focusbudget
   sudo systemctl status focusbudget
   ```

### **Option 2: Secure Deployment (Recommended)**
For a production-ready, secure setup:

1. **Run the secure deployment script:**
   ```bash
   sudo ./build-and-deploy-secure.sh
   ```

2. **The script will:**
   - Create dedicated user and secure directory structure
   - Apply all security hardening measures
   - Migrate your existing data safely
   - Set up proper permissions and ownership

3. **After deployment:**
   - Application runs as `focusbudget_user`
   - Data stored in `/var/lib/focusbudget/`
   - Logs in `/var/log/focusbudget/`
   - Enhanced security and monitoring

## Verification Steps

### **Check Service Status**
```bash
sudo systemctl status focusbudget
sudo journalctl -u focusbudget --since "5 minutes ago"
```

### **Test Application**
```bash
curl http://localhost/health
curl http://localhost/api/status
```

### **Verify Security**
```bash
# Check user
id focusbudget_user

# Check file permissions
ls -la /opt/focusbudget/
ls -la /var/lib/focusbudget/

# Check service security
systemctl show focusbudget | grep -E "(User|Group|NoNewPrivileges|PrivateTmp)"
```

## Benefits of Secure Deployment

1. **Isolation:** Application runs in its own security context
2. **Principle of Least Privilege:** Minimal permissions required
3. **Attack Surface Reduction:** Limited file system and network access
4. **Monitoring:** Centralized logging and status monitoring
5. **Maintainability:** Standard Linux service management
6. **Compliance:** Follows security best practices for production deployments

## Recommendation

**Use the secure deployment script** (`build-and-deploy-secure.sh`) for production environments. It addresses both the immediate routing issue and implements comprehensive security measures that are essential for any production web application.
