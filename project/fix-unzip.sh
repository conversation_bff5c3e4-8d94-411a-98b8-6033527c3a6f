#!/bin/bash

# Quick fix to install unzip and continue deployment

REMOTE_HOST="**********"
REMOTE_USER="labmaster"
REMOTE_TARGET="${REMOTE_USER}@${REMOTE_HOST}"

echo "Installing unzip on remote server..."
ssh "${REMOTE_TARGET}" "sudo apt-get update && sudo apt-get install -y unzip"

echo "Finding the staging directory..."
STAGING_DIR=$(ssh "${REMOTE_TARGET}" "ls -td /tmp/focusbudget-staging-* 2>/dev/null | head -1")

if [ -n "$STAGING_DIR" ]; then
  echo "Found staging directory: $STAGING_DIR"
  echo "Extracting package..."
  
  ssh "${REMOTE_TARGET}" "
    cd '$STAGING_DIR'
    echo 'Current directory contents:'
    ls -la
    
    if [ -f package.zip ]; then
      echo 'Extracting package.zip...'
      unzip -o package.zip
      echo 'Extraction complete. Contents:'
      ls -la
    else
      echo 'No package.zip found'
    fi
  "
else
  echo "No staging directory found"
fi
