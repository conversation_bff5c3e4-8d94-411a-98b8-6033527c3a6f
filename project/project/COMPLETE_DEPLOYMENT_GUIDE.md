# FocusBudget Complete Deployment Guide

## Overview

The `deploy-complete.sh` script provides a comprehensive, end-to-end deployment solution for FocusBudget. It handles everything from local prerequisites to remote validation, ensuring a clean, secure, and properly configured deployment.

## What This Script Does

### **Complete 10-Step Process:**

1. **Install Local Prerequisites** - Ensures zip, ssh, rsync, curl are available
2. **Validate Local Project** - Checks project structure and extracts version
3. **Setup SSH Authentication** - Configures SSH keys if not already set up
4. **Build Application** - Runs npm install and npm run build locally
5. **Create Deployment Package** - Creates a zip file with all necessary files
6. **Setup Remote Directories** - Creates staging and backup directories
7. **Transfer Package** - Uploads and extracts the deployment package
8. **Setup Service Account** - Creates dedicated user, installs app, sets permissions
9. **Configure Services** - Sets up systemd service and Nginx reverse proxy
10. **Test and Validate** - Comprehensive testing and version validation

## Prerequisites

- **Local machine**: Linux/macOS with bash
- **Remote server**: Ubuntu 24.04 LXC container
- **Network access**: SSH connectivity to remote server
- **Permissions**: sudo access on remote server

## Usage

### **Simple Execution**
```bash
cd /BudgetPage/project
./deploy-complete.sh
```

### **What Happens During Deployment**

#### **Local Setup (Steps 1-5)**
- Installs missing tools (zip, ssh, rsync, curl)
- Validates project structure and gets version from package.json
- Sets up SSH key authentication (prompts for password if needed)
- Builds the application locally with npm
- Creates a deployment zip package

#### **Remote Setup (Steps 6-10)**
- Creates staging directory and backs up existing deployment
- Transfers and extracts the deployment package
- Creates `focusbudget_user` system account with restricted permissions
- Installs Node.js dependencies and configures file permissions
- Sets up systemd service with security hardening
- Configures Nginx reverse proxy on port 80
- Tests all endpoints and validates version

## Security Features

### **System User Isolation**
- Dedicated `focusbudget_user` system account
- No shell access (`/bin/false`)
- Restricted home directory
- Group-based permissions

### **File System Security**
- Application files: `/opt/focusbudget/` (read-only for app)
- Data directory: `/var/lib/focusbudget/` (read-write for app only)
- Log directory: `/var/log/focusbudget/` (write-only for app)
- Proper ownership and permissions (644/755/750)

### **Systemd Security Hardening**
- `NoNewPrivileges=true`
- `PrivateTmp=true`
- `PrivateDevices=true`
- `ProtectHome=true`
- `ProtectSystem=strict`
- Read-only application directory
- Restricted file system access

### **Nginx Security**
- Security headers (XSS protection, content type sniffing prevention)
- Server token hiding
- Request size limits
- Proper proxy headers

## Version Management

### **Version Display**
- Version number shown in application header (blue badge)
- Extracted from `package.json` version field
- Currently set to `1.0.0`

### **Version Endpoints**
- **`/api/version`** - Returns version, name, and deployment timestamp
- **`/api/status`** - Returns status, version, uptime, and error statistics

### **Version Validation**
- Script validates deployed version matches expected version
- Programmatic version checking via API endpoints
- Visual version display in application header

## Directory Structure

```
/opt/focusbudget/              # Application root
├── dist/                      # Built frontend files
├── server.cjs                 # Backend server
├── package.json               # Dependencies and version
├── node_modules/              # Node.js dependencies
├── src/                       # Source files (for reference)
└── .env                       # Environment configuration

/var/lib/focusbudget/          # Data directory
├── transactions.json          # Transaction data
├── budget.json                # Budget configuration
└── history/                   # Monthly history files

/var/log/focusbudget/          # Log directory
└── (application logs)

/etc/systemd/system/           # Service configuration
└── focusbudget.service

/etc/nginx/sites-available/    # Nginx configuration
└── focusbudget
```

## Service Management

### **Service Commands**
```bash
# Check status
ssh labmaster@********** 'sudo systemctl status focusbudget'

# View logs
ssh labmaster@********** 'sudo journalctl -u focusbudget -f'

# Restart service
ssh labmaster@********** 'sudo systemctl restart focusbudget'

# Check Nginx
ssh labmaster@********** 'sudo systemctl status nginx'
```

### **Version Checking**
```bash
# Check deployed version
curl -s http://**********/api/version

# Check application status
curl -s http://**********/api/status

# Test health endpoint
curl -s http://**********/health
```

## Access Points

After successful deployment:

- **Primary Application**: http://**********
- **Health Check**: http://**********/health
- **Version Info**: http://**********/api/version
- **API Status**: http://**********/api/status

## Troubleshooting

### **SSH Issues**
```bash
# Test SSH connection
ssh labmaster@********** "echo 'Connection works'"

# Generate new SSH key if needed
ssh-keygen -t ed25519 -C "<EMAIL>"

# Copy SSH key to server
ssh-copy-id labmaster@**********
```

### **Service Issues**
```bash
# Check service logs
ssh labmaster@********** 'sudo journalctl -u focusbudget --since "10 minutes ago"'

# Check what's listening on ports
ssh labmaster@********** 'sudo netstat -tlnp | grep -E ":(80|3001)"'

# Test direct backend connection
curl -s http://**********:3001/api/status
```

### **Version Mismatch**
If deployed version doesn't match expected:
1. Check if deployment completed successfully
2. Verify package.json version is correct
3. Re-run deployment script
4. Check application logs for errors

## Backup and Recovery

### **Automatic Backups**
- Each deployment creates automatic backup in `/tmp/focusbudget-backup-TIMESTAMP/`
- Includes both application files and data directory
- Backup location displayed at end of deployment

### **Manual Backup**
```bash
ssh labmaster@********** "
  sudo mkdir -p /tmp/manual-backup-$(date +%Y%m%d)
  sudo cp -r /opt/focusbudget /tmp/manual-backup-$(date +%Y%m%d)/
  sudo cp -r /var/lib/focusbudget /tmp/manual-backup-$(date +%Y%m%d)/
"
```

## Clean Deployment Process

This script is designed for clean deployments. It will:
- ✅ **Completely replace** the existing application
- ✅ **Preserve data** in `/var/lib/focusbudget/`
- ✅ **Create backups** before making changes
- ✅ **Validate everything** works before completing
- ✅ **Provide clear feedback** at each step

## Success Indicators

A successful deployment will show:
- ✅ All 10 steps completed successfully
- ✅ Services running (focusbudget and nginx)
- ✅ All connectivity tests passing
- ✅ Version validation matching expected version
- ✅ Application accessible at http://**********

The script provides comprehensive feedback and will stop on any errors, making it safe to re-run if issues occur.
