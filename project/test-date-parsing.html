<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Date Format Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .pass { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .fail { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .test-input { margin: 5px 0; font-family: monospace; }
    </style>
</head>
<body>
    <h1>Date Format Parsing Test</h1>
    <p>Testing the date normalization function with various input formats:</p>
    
    <div id="test-results"></div>

    <script>
        // Copy of the normalizeDateFormat function from DataTab.tsx
        const normalizeDateFormat = (dateString) => {
            // Handle DD/MM/YYYY format (common in non-US countries)
            if (dateString.includes('/') && dateString.split('/').length === 3) {
                const parts = dateString.split('/');
                if (parts[0].length <= 2 && parts[1].length <= 2 && parts[2].length === 4) {
                    // DD/MM/YYYY format
                    const day = parts[0].padStart(2, '0');
                    const month = parts[1].padStart(2, '0');
                    const year = parts[2];
                    return `${year}-${month}-${day}`;
                }
            }
            
            // Handle DD-MM-YYYY format
            if (dateString.includes('-') && dateString.split('-').length === 3) {
                const parts = dateString.split('-');
                if (parts[0].length <= 2 && parts[1].length <= 2 && parts[2].length === 4) {
                    // DD-MM-YYYY format
                    const day = parts[0].padStart(2, '0');
                    const month = parts[1].padStart(2, '0');
                    const year = parts[2];
                    return `${year}-${month}-${day}`;
                }
            }
            
            // Handle YYYY-MM-DD format (already correct)
            if (dateString.includes('-') && dateString.split('-').length === 3) {
                const parts = dateString.split('-');
                if (parts[0].length === 4 && parts[1].length <= 2 && parts[2].length <= 2) {
                    // Already in YYYY-MM-DD format, just ensure padding
                    const year = parts[0];
                    const month = parts[1].padStart(2, '0');
                    const day = parts[2].padStart(2, '0');
                    return `${year}-${month}-${day}`;
                }
            }
            
            // Try to parse with Date constructor and format as YYYY-MM-DD
            try {
                const date = new Date(dateString);
                if (!isNaN(date.getTime())) {
                    const year = date.getFullYear();
                    const month = (date.getMonth() + 1).toString().padStart(2, '0');
                    const day = date.getDate().toString().padStart(2, '0');
                    return `${year}-${month}-${day}`;
                }
            } catch (error) {
                console.warn('Failed to parse date:', dateString);
            }
            
            // Fallback: return original string
            return dateString;
        };

        // Test cases
        const testCases = [
            // DD/MM/YYYY format
            { input: '15/10/2024', expected: '2024-10-15', description: 'DD/MM/YYYY format' },
            { input: '5/3/2024', expected: '2024-03-05', description: 'D/M/YYYY format (single digits)' },
            { input: '31/12/2023', expected: '2023-12-31', description: 'DD/MM/YYYY end of year' },
            
            // DD-MM-YYYY format
            { input: '15-10-2024', expected: '2024-10-15', description: 'DD-MM-YYYY format' },
            { input: '5-3-2024', expected: '2024-03-05', description: 'D-M-YYYY format (single digits)' },
            { input: '01-01-2024', expected: '2024-01-01', description: 'DD-MM-YYYY new year' },
            
            // YYYY-MM-DD format (already correct)
            { input: '2024-10-15', expected: '2024-10-15', description: 'YYYY-MM-DD format (already correct)' },
            { input: '2024-3-5', expected: '2024-03-05', description: 'YYYY-M-D format (needs padding)' },
            
            // Edge cases
            { input: '29/02/2024', expected: '2024-02-29', description: 'Leap year date' },
            { input: '1/1/2024', expected: '2024-01-01', description: 'Single digit day and month' }
        ];

        // Run tests
        const resultsDiv = document.getElementById('test-results');
        let passCount = 0;
        let totalCount = testCases.length;

        testCases.forEach((testCase, index) => {
            const result = normalizeDateFormat(testCase.input);
            const passed = result === testCase.expected;
            
            if (passed) passCount++;

            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${passed ? 'pass' : 'fail'}`;
            resultDiv.innerHTML = `
                <strong>Test ${index + 1}: ${testCase.description}</strong><br>
                <div class="test-input">Input: "${testCase.input}"</div>
                <div class="test-input">Expected: "${testCase.expected}"</div>
                <div class="test-input">Got: "${result}"</div>
                <div class="test-input">Status: ${passed ? '✅ PASS' : '❌ FAIL'}</div>
            `;
            resultsDiv.appendChild(resultDiv);
        });

        // Summary
        const summaryDiv = document.createElement('div');
        summaryDiv.className = `test-result ${passCount === totalCount ? 'pass' : 'fail'}`;
        summaryDiv.innerHTML = `
            <strong>Test Summary</strong><br>
            Passed: ${passCount}/${totalCount} tests<br>
            Success Rate: ${Math.round((passCount / totalCount) * 100)}%
        `;
        resultsDiv.appendChild(summaryDiv);

        console.log(`Date format tests completed: ${passCount}/${totalCount} passed`);
    </script>
</body>
</html>
