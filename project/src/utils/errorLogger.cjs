/**
 * Enhanced error logging utility for BudgetPage
 * Provides structured logging and error tracking for improved debugging
 */

const fs = require('fs');
const path = require('path');

// Configure log directory
const LOG_DIR = process.env.LOG_DIR || './logs';

// Create log directory if it doesn't exist
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR, { recursive: true });
}

// Format log entry with timestamp and level
function formatLogEntry(level, message, data) {
  const timestamp = new Date().toISOString();
  return {
    timestamp,
    level,
    message,
    ...data
  };
}

// Write log to console with formatting
function consoleLog(level, message, data = null) {
  const timestamp = new Date().toISOString();
  const prefix = `[${timestamp}] [${level.toUpperCase()}]`;
  
  if (data) {
    console[level.toLowerCase()](prefix, message, data);
  } else {
    console[level.toLowerCase()](prefix, message);
  }
}

// Write log to file
function fileLog(entry) {
  try {
    const logFile = path.join(LOG_DIR, `budget-app-${new Date().toISOString().split('T')[0]}.log`);
    const logData = `${JSON.stringify(entry)}\n`;
    
    fs.appendFileSync(logFile, logData);
  } catch (error) {
    console.error('Failed to write to log file:', error);
  }
}

// Track JSON parsing errors specifically
const jsonErrors = {
  _errors: [],
  
  // Add a new JSON error to the tracking
  add(collection, error, filePath) {
    const errorInfo = {
      timestamp: new Date().toISOString(),
      collection,
      filePath,
      message: error.message,
      position: error.message.match(/position (\d+)/)?.[1] || 'unknown',
      stackTrace: error.stack
    };
    
    this._errors.push(errorInfo);
    return errorInfo;
  },
  
  // Get all tracked JSON errors
  getAll() {
    return this._errors;
  },
  
  // Get summary of JSON errors by collection
  getSummary() {
    const summary = {};
    
    this._errors.forEach(error => {
      if (!summary[error.collection]) {
        summary[error.collection] = {
          count: 0,
          examples: []
        };
      }
      
      summary[error.collection].count++;
      
      if (summary[error.collection].examples.length < 3) {
        summary[error.collection].examples.push({
          timestamp: error.timestamp,
          message: error.message,
          position: error.position
        });
      }
    });
    
    return summary;
  },
  
  // Clear all tracked errors
  clear() {
    this._errors = [];
  }
};

// Main logger object 
const logger = {
  // Log levels
  info: (message, data) => {
    const entry = formatLogEntry('info', message, data);
    consoleLog('info', message, data);
    fileLog(entry);
  },
  
  warn: (message, data) => {
    const entry = formatLogEntry('warn', message, data);
    consoleLog('warn', message, data);
    fileLog(entry);
  },
  
  error: (message, data) => {
    const entry = formatLogEntry('error', message, data);
    consoleLog('error', message, data);
    fileLog(entry);
  },
  
  debug: (message, data) => {
    const entry = formatLogEntry('debug', message, data);
    consoleLog('debug', message, data);
    fileLog(entry);
  },
  
  // Specialized logging for JSON parsing errors
  jsonParsingError: (collection, error, filePath, fix = false) => {
    const errorInfo = jsonErrors.add(collection, error, filePath);
    
    const logData = {
      collection,
      filePath,
      position: errorInfo.position,
      error: error.message,
      fixed: fix
    };
    
    consoleLog('error', `JSON parsing error for ${collection}:`, logData);
    fileLog(formatLogEntry('error', `JSON parsing error for ${collection}`, logData));
    
    return errorInfo;
  },
  
  // Record successful auto-fix of JSON data
  jsonAutoFixed: (collection, filePath, info = {}) => {
    const logData = {
      collection,
      filePath,
      fixedAt: new Date().toISOString(),
      ...info
    };
    
    consoleLog('info', `Auto-fixed and saved JSON data for ${collection}`, logData);
    fileLog(formatLogEntry('info', `Auto-fixed JSON data for ${collection}`, logData));
  },
  
  // Access to JSON error tracking
  jsonErrors
};

module.exports = logger;
