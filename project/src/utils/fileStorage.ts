// File-based storage helper for persisting data to server

export const fileStorage = {
  // Base API URL - can be configured if needed
  apiUrl: 'http://localhost:3001/api',
  
  // Flag to track if file storage is available
  isAvailable: false,
  
  // Initialize and check if file storage is available
  async init(): Promise<boolean> {
    try {
      const response = await fetch(`${this.apiUrl}/storage/status`);
      const data = await response.json();
      this.isAvailable = data.status === 'available';
      return this.isAvailable;
    } catch (error) {
      console.warn('File storage is not available:', error);
      this.isAvailable = false;
      return false;
    }
  },
  
  // Get data from a collection
  async getData(collection: string): Promise<any> {
    if (!this.isAvailable) {
      return null;
    }
    
    try {
      const response = await fetch(`${this.apiUrl}/storage/${collection}`);
      const result = await response.json();
      return result.data;
    } catch (error) {
      console.error(`Error getting ${collection} data:`, error);
      return null;
    }
  },
  
  // Save data to a collection
  async saveData(collection: string, data: any): Promise<boolean> {
    if (!this.isAvailable) {
      return false;
    }
    
    try {
      const response = await fetch(`${this.apiUrl}/storage/${collection}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ data }),
      });
      
      const result = await response.json();
      return result.success === true;
    } catch (error) {
      console.error(`Error saving ${collection} data:`, error);
      return false;
    }
  },
  
  // Get monthly history data
  async getMonthlyHistory(year: number, month: string): Promise<any> {
    if (!this.isAvailable) {
      return null;
    }
    
    try {
      const response = await fetch(`${this.apiUrl}/storage/history/${year}/${month}`);
      if (response.status === 404) {
        return null;
      }
      const result = await response.json();
      return result.data;
    } catch (error) {
      console.error(`Error getting history for ${year}-${month}:`, error);
      return null;
    }
  },
  
  // Save monthly history data
  async saveMonthlyHistory(year: number, month: string, data: any): Promise<boolean> {
    if (!this.isAvailable) {
      return false;
    }
    
    try {
      const response = await fetch(`${this.apiUrl}/storage/history/${year}/${month}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ data }),
      });
      
      const result = await response.json();
      return result.success === true;
    } catch (error) {
      console.error(`Error saving history for ${year}-${month}:`, error);
      return false;
    }
  },
  
  // Delete monthly history data
  async deleteMonthlyHistory(year: number, month: string): Promise<boolean> {
    if (!this.isAvailable) {
      return false;
    }
    
    try {
      const response = await fetch(`${this.apiUrl}/storage/history/${year}/${month}`, {
        method: 'DELETE',
      });
      
      const result = await response.json();
      return result.success === true;
    } catch (error) {
      console.error(`Error deleting history for ${year}-${month}:`, error);
      return false;
    }
  },
  
  // Clear all data
  async clearAll(): Promise<boolean> {
    if (!this.isAvailable) {
      return false;
    }
    
    try {
      const response = await fetch(`${this.apiUrl}/storage/clear`, {
        method: 'POST',
      });
      
      const result = await response.json();
      return result.success === true;
    } catch (error) {
      console.error('Error clearing all data:', error);
      return false;
    }
  },
  
  // Get the OpenAI API key from environment
  async getApiKey(): Promise<string> {
    try {
      const response = await fetch(`${this.apiUrl}/config/openai`);
      const result = await response.json();
      return result.apiKey || '';
    } catch (error) {
      console.error('Error getting API key:', error);
      return '';
    }
  }
};

// Initialize file storage on import
fileStorage.init().catch(console.error);
