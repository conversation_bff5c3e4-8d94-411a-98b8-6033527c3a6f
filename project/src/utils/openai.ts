import { OpenAIRequest, OpenAIResponse, OpenAIModel } from '../types';
import { storage } from './storage';
import { OPENAI_API_KEY } from './env';

// Define which models use chat completions vs. regular completions
const CHAT_MODELS = [
  'gpt-3.5-turbo', 
  'gpt-3.5-turbo-0125', 
  'gpt-4', 
  'gpt-4-0125-preview', 
  'gpt-4-turbo-preview', 
  'gpt-4-turbo', 
  'gpt-4-1106-preview'
];
const COMPLETION_MODELS = [
  'gpt-3.5-turbo-instruct', 
  'text-davinci-003', 
  'text-davinci-002'
];

// Define token limits for different models
const MODEL_TOKEN_LIMITS: Record<string, {contextLimit: number, defaultMaxTokens: number}> = {
  // Completion models
  'gpt-3.5-turbo-instruct': { contextLimit: 4097, defaultMaxTokens: 1000 },
  'text-davinci-003': { contextLimit: 4097, defaultMaxTokens: 1000 },
  'text-davinci-002': { contextLimit: 4097, defaultMaxTokens: 1000 },
  
  // Chat models
  'gpt-3.5-turbo': { contextLimit: 16385, defaultMaxTokens: 2000 },
  'gpt-3.5-turbo-0125': { contextLimit: 16385, defaultMaxTokens: 2000 },
  'gpt-4': { contextLimit: 8192, defaultMaxTokens: 2000 },
  'gpt-4-0125-preview': { contextLimit: 128000, defaultMaxTokens: 4000 },
  'gpt-4-turbo-preview': { contextLimit: 128000, defaultMaxTokens: 4000 },
  'gpt-4-turbo': { contextLimit: 128000, defaultMaxTokens: 4000 },
  'gpt-4-1106-preview': { contextLimit: 128000, defaultMaxTokens: 4000 }
};

// Default fallback for any model not explicitly listed
const DEFAULT_TOKEN_LIMIT = { contextLimit: 4097, defaultMaxTokens: 1000 };

export class OpenAIService {
  private apiKey: string;
  private model: OpenAIModel = 'gpt-3.5-turbo-instruct';

  constructor(apiKey: string = '') {
    // Try to get the API key from environment variables if not provided
    this.apiKey = apiKey || OPENAI_API_KEY;
  }
  
  setApiKey(apiKey: string): void {
    this.apiKey = apiKey;
  }
  
  setModel(model: OpenAIModel): void {
    this.model = model;
  }
  
  /**
   * Attempt to repair common JSON syntax issues
   * @param json Potentially malformed JSON string
   * @returns Repaired JSON string
   */
  private repairJson(json: string): string {
    // First, log the problematic JSON for debugging
    console.log('Attempting to repair JSON, length:', json.length);
    
    let result = json;
    
    // Remove trailing commas in arrays and objects
    result = result.replace(/,\s*([\]}])/g, '$1');
    
    // Add missing quotes around property names
    result = result.replace(/([{,])\s*([a-zA-Z0-9_]+)\s*:/g, '$1"$2":');
    
    // Fix missing quotes around string values
    // This is a simplistic approach and might not catch all cases
    result = result.replace(/:\s*([a-zA-Z][a-zA-Z0-9_]*)\s*([,}])/g, ':"$1"$2');
    
    // Replace single quotes with double quotes
    result = result.replace(/'([^']*)'/g, '"$1"');
    
    // Add missing quotes around date strings (common pattern)
    result = result.replace(/:\s*(\d{4}-\d{2}-\d{2})\s*([,}])/g, ':"$1"$2');
    
    // Fix boolean values (ensure they're not quoted)
    result = result.replace(/"true"/g, 'true').replace(/"false"/g, 'false');
    
    // Fix number values (ensure they're not quoted if they're actually numbers)
    result = result.replace(/"([-+]?[0-9]*\.?[0-9]+)"/g, (match, p1) => {
      // Check if p1 is a valid number
      if (!isNaN(Number(p1))) {
        return p1;
      }
      // If not a valid number, keep the original quoted string
      return match;
    });
    
    // Fix common structural issues
    
    // 1. Remove any empty properties (like ,, or ,})
    result = result.replace(/,\s*,/g, ','); // Double commas
    
    // 2. Fix missing closing braces by counting opening and closing braces
    const openBraces = (result.match(/{/g) || []).length;
    const closeBraces = (result.match(/}/g) || []).length;
    if (openBraces > closeBraces) {
      // Add missing closing braces
      result += '}'.repeat(openBraces - closeBraces);
    }
    
    // 3. Fix missing closing brackets by counting opening and closing brackets
    const openBrackets = (result.match(/\[/g) || []).length;
    const closeBrackets = (result.match(/\]/g) || []).length;
    if (openBrackets > closeBrackets) {
      // Add missing closing brackets
      result += ']'.repeat(openBrackets - closeBrackets);
    }
    
    // 4. Handle malformed nested objects
    result = result.replace(/:\s*}/g, ':null}'); // Replace empty object values with null
    
    // 5. Handle trailing characters after json
    const possibleJson = result.match(/^\s*\{.*?\}\s*/s);
    if (possibleJson) {
      result = possibleJson[0];
    }
    
    return result;
  }

  
  /**
   * Get appropriate chunk size based on the current model
   * @returns The maximum number of rows per chunk for the current model
   */
  private getChunkSize(): number {
    const modelLimit = MODEL_TOKEN_LIMITS[this.model] || DEFAULT_TOKEN_LIMIT;
    
    // Scale rows based on context limit - larger models can handle more rows
    // Lower limits provide more safety margin
    if (modelLimit.contextLimit >= 128000) {
      return 200; // For the largest models (GPT-4 Turbo)
    } else if (modelLimit.contextLimit >= 16000) {
      return 120; // For GPT-3.5 Turbo with 16k context
    } else if (modelLimit.contextLimit >= 8000) {
      return 90;  // For GPT-4 standard
    } else {
      return 40;  // For models with 4k context like gpt-3.5-turbo-instruct
    }
  }
  
  /**
   * Get appropriate max tokens for completion based on the current model
   * @returns The maximum number of tokens for completion
   */
  private getMaxTokens(): number {
    const modelLimit = MODEL_TOKEN_LIMITS[this.model] || DEFAULT_TOKEN_LIMIT;
    return modelLimit.defaultMaxTokens;
  }

  /**
   * Split CSV data into chunks that can be processed by the API
   * @param csvData Full CSV data string
   * @returns Array of CSV chunks with headers intact
   */
  private splitCsvIntoChunks(csvData: string): string[] {
    const maxRows = this.getChunkSize();
    const lines = csvData.split('\n');
    const header = lines[0];
    if (lines.length <= maxRows + 1) { // +1 for header
      return [csvData];
    }
    
    const chunks: string[] = [];
    let currentChunk: string[] = [];
    
    for (let i = 1; i < lines.length; i++) {
      if (currentChunk.length === 0) {
        currentChunk.push(header); // Add header to each chunk
      }
      
      if (lines[i].trim()) { // Skip empty lines
        currentChunk.push(lines[i]);
      }
      
      if (currentChunk.length > maxRows || i === lines.length - 1) {
        chunks.push(currentChunk.join('\n'));
        currentChunk = [];
      }
    }
    
    return chunks;
  }

  /**
   * Process a single chunk of CSV data with the OpenAI API
   */
  private async processCsvChunk(csvChunk: string, categoryNames: string[], categoriesString: string, keywordGuidance: string): Promise<any> {
    const MAX_RETRIES = 3;
    let attempt = 0;
    let delay = 2000; // Start with 2 seconds
    let lastError: Error | null = null;
    
    while (attempt < MAX_RETRIES) {
      const request: OpenAIRequest = {
        input_csv: csvChunk,
        instruction: `Categorize each transaction into one of these specific budget categories: ${categoriesString}. If a transaction doesn't clearly fit any category, use "Miscellaneous". IMPORTANT: Input dates are in DD/MM/YYYY format (day/month/year) - convert them to YYYY-MM-DD format in your response. The data may span multiple months. For internal transfers between accounts (same date, same amount but opposite signs), categorize as "Transfer". Return as JSON with date (YYYY-MM-DD format), description, category (must be exactly one of the provided categories), and amount (negative for expenses, positive for income).${keywordGuidance}`,
        model: this.model
      };

      try {
        let response;
        
        // For chat models (GPT-3.5 Turbo, GPT-4), use the chat completions endpoint
        if (CHAT_MODELS.includes(this.model)) {
          response = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${this.apiKey}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              model: this.model,
              messages: [
                {
                  role: 'system',
                  content: `You are a financial categorization assistant. Analyze CSV transaction data and categorize each transaction into one of these specific categories: ${categoriesString}. You must use exactly these category names - do not create new categories. Return only valid JSON in the specified format.${keywordGuidance}`
                },
                {
                  role: 'user',
                  content: `Please categorize this CSV data using only these categories: ${categoriesString}\n\nCSV Data: ${request.input_csv}\n\nInstructions: ${request.instruction}\n\nReturn format:\n{\n  "categorized": [\n    { "date": "YYYY-MM-DD", "description": "...", "category": "exact category name from list", "amount": number },\n    ...\n  ]\n}`
                }
              ],
              temperature: 0.2, // Lower temperature for more consistent categorization
            }),
          });
        } 
        // For completions models (GPT-3.5 Turbo Instruct), use the completions endpoint
        else if (COMPLETION_MODELS.includes(this.model)) {
          const prompt = `You are a financial categorization assistant. Analyze CSV transaction data and categorize each transaction into one of these specific categories: ${categoriesString}. You must use exactly these category names - do not create new categories. Return only valid JSON in the specified format.${keywordGuidance}\n\nPlease categorize this CSV data using only these categories: ${categoriesString}\n\nCSV Data: ${request.input_csv}\n\nInstructions: ${request.instruction}\n\nReturn format:\n{\n  "categorized": [\n    { "date": "YYYY-MM-DD", "description": "...", "category": "exact category name from list", "amount": number },\n    ...\n  ]\n}`;
          
          response = await fetch('https://api.openai.com/v1/completions', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${this.apiKey}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              model: this.model,
              prompt: prompt,
              temperature: 0.2,
              max_tokens: this.getMaxTokens(), // Dynamically set based on model
            }),
          });
        }
        // Fallback for any other model types
        else {
          throw new Error(`Unsupported model type: ${this.model}`);
        }
    
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(`OpenAI API error: ${response.statusText} ${errorData?.error?.message || ''}`);
        }
        
        const data = await response.json();
        
        // Extract content based on model type (chat models vs completion models have different response formats)
        let content;
        if (CHAT_MODELS.includes(this.model)) {
          content = data.choices[0]?.message?.content;
        } else if (COMPLETION_MODELS.includes(this.model)) {
          content = data.choices[0]?.text;
        }
        
        if (!content) {
          throw new Error('No response from OpenAI');
        }
    
        // Parse the JSON response
        try {
          // First, try direct parsing
          let parsed = JSON.parse(content);
          
          // Validate that all categories in the response are valid
          if (parsed.categorized && Array.isArray(parsed.categorized)) {
            parsed.categorized = parsed.categorized.map((item: any) => {
              // Ensure the category exists in our current categories
              if (!categoryNames.includes(item.category)) {
                item.category = 'Miscellaneous';
              }
              return item;
            });
          }
          
          return parsed;
        } catch (parseError: any) {
          console.error('JSON parsing error:', parseError.message);
          console.log('Content length:', content.length);
          
          // Log problematic part of the content around the error position
          const errorPosition = parseError.message.match(/position (\d+)/);
          if (errorPosition && errorPosition[1]) {
            const pos = parseInt(errorPosition[1]);
            const start = Math.max(0, pos - 50);
            const end = Math.min(content.length, pos + 50);
            console.log(`Content around error (${start}-${end}):`, content.substring(start, end));
            console.log('Error position marked:', content.substring(start, pos) + ' 👉 HERE 👈 ' + content.substring(pos, end));
          }
          
          // Try different JSON repair strategies
          try {
            // Strategy 0: Simplest approach - try repairing the entire content directly
            try {
              const directRepair = this.repairJson(content);
              const parsed = JSON.parse(directRepair);
              if (parsed.categorized && Array.isArray(parsed.categorized)) {
                return parsed;
              }
            } catch (directError) {
              // Continue with other strategies
              console.log('Direct repair failed, trying extraction strategies');
            }
            
            // Strategy 1: Extract JSON from the response
            const jsonMatch = content.match(/\{[\s\S]*\}/s);
            if (jsonMatch) {
              // Get the matched JSON and try to clean it before parsing
              let jsonContent = jsonMatch[0];
              console.log('Found JSON match, length:', jsonContent.length);
              
              // Simple JSON repair attempts
              jsonContent = this.repairJson(jsonContent);
              
              try {
                const parsed = JSON.parse(jsonContent);
                
                // Validate categories here too
                if (parsed.categorized && Array.isArray(parsed.categorized)) {
                  parsed.categorized = parsed.categorized.map((item: any) => {
                    if (!categoryNames.includes(item.category)) {
                      item.category = 'Miscellaneous';
                    }
                    return item;
                  });
                  return parsed;
                }
              } catch (matchError: any) {
                console.log('JSON match parsing failed:', matchError.message);
              }
            }
            
            // Strategy 2: Look for categorized array directly
            const categorizedMatch = content.match(/"categorized"\s*:\s*\[([\s\S]*?)\]/s);
            if (categorizedMatch) {
              const categorizedContent = categorizedMatch[0];
              console.log('Found categorized array, length:', categorizedContent.length);
              const repaired = '{' + categorizedContent + '}';
              const cleanedJson = this.repairJson(repaired);
              
              try {
                const parsed = JSON.parse(cleanedJson);
                if (parsed.categorized && Array.isArray(parsed.categorized)) {
                  return parsed;
                }
              } catch (arrayError: any) {
                console.log('Categorized array parsing failed:', arrayError.message);
              }
            }
            
            // Strategy 3: Manual JSON reconstruction from transaction entries
            const transactionMatches = content.match(/\{\s*"date"\s*:\s*"[^"]*"\s*,\s*"description"\s*:\s*"[^"]*"\s*,\s*"category"\s*:\s*"[^"]*"\s*,\s*"amount"\s*:\s*[-+]?[0-9]*\.?[0-9]+\s*\}/gs);
            if (transactionMatches && transactionMatches.length > 0) {
              console.log(`Found ${transactionMatches.length} individual transactions, trying to reconstruct`);
              let reconstructed = '{ "categorized": [' + transactionMatches.join(',') + ']}'; 
              
              try {
                const parsed = JSON.parse(reconstructed);
                if (parsed.categorized && Array.isArray(parsed.categorized)) {
                  // Validate categories
                  parsed.categorized = parsed.categorized.map((item: any) => {
                    if (!categoryNames.includes(item.category)) {
                      item.category = 'Miscellaneous';
                    }
                    return item;
                  });
                  return parsed;
                }
              } catch (reconstructError: any) {
                console.log('Reconstruction parsing failed:', reconstructError.message);
              }
            }
            
            // If we got here, all repair attempts failed
            throw new Error(`Failed to repair JSON: ${parseError.message}`);
          } catch (repairError) {
            console.error('JSON repair failed:', repairError);
            throw new Error(`Invalid JSON response from OpenAI. Original error: ${parseError.message}. Position: ${parseError.message.match(/position (\d+)/)?.[1] || 'unknown'}`);
          }
        }
      } catch (error: any) {
        lastError = error;
        
        if ((error.message.includes('rate limit') || 
            error.message.includes('quota exceeded') ||
            error.message.includes('429') ||
            error.message.includes('capacity')) && attempt < MAX_RETRIES - 1) {
          
          console.log(`API request failed (${attempt + 1}/${MAX_RETRIES}). Retrying in ${delay/1000} seconds...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          delay *= 2; // Exponential backoff
          attempt++;
        } else {
          throw error; // Re-throw for non-rate limit errors or final attempt
        }
      }
    }
    
    // End of the while loop - if we get here, all retries failed
    if (lastError) throw lastError;
    throw new Error('Failed to process CSV after multiple attempts');
  }
  /**
   * Process a CSV file by chunking it and categorizing all transactions
   * @param csvData The full CSV data to process
   */
  async categorizeTransactions(csvData: string): Promise<OpenAIResponse> {
    if (!this.apiKey) {
      throw new Error('OpenAI API key is required');
    }

    // Get current category names and keyword mappings for the AI prompt
    const categoryNames = await storage.getCategoryNames();
    const keywordMappings = await storage.getKeywordMappings();
    const categoriesString = categoryNames.join(', ');

    // Build keyword guidance for the AI
    let keywordGuidance = '';
    if (keywordMappings.length > 0) {
      keywordGuidance = '\n\nIMPORTANT KEYWORD GUIDANCE - Pay special attention to these keyword-to-category mappings:\n';
      keywordMappings.forEach(mapping => {
        keywordGuidance += `- If transaction description contains "${mapping.keyword}", strongly consider categorizing as "${mapping.categoryName}"\n`;
      });
    }

    try {
      // Split CSV into manageable chunks to avoid API token limits
      const chunks = this.splitCsvIntoChunks(csvData);
      console.log(`CSV data split into ${chunks.length} chunks for processing using ${this.model} (chunk size: ${this.getChunkSize()} rows)`);
      
      let allCategorized: any[] = [];
      
      // Process each chunk sequentially to avoid rate limits
      for (let i = 0; i < chunks.length; i++) {
        console.log(`Processing chunk ${i + 1} of ${chunks.length}`);
        const chunkResult = await this.processCsvChunk(chunks[i], categoryNames, categoriesString, keywordGuidance);
        
        if (chunkResult.categorized && Array.isArray(chunkResult.categorized)) {
          allCategorized = [...allCategorized, ...chunkResult.categorized];
        }
      }
      
      return { categorized: allCategorized };
    } catch (error: any) {
      console.error('OpenAI categorization error:', error);
      throw error;
    }
  }
}

export const openaiService = new OpenAIService();