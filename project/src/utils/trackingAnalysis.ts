import { Transaction, TrackedExpense } from '../types';

interface RecurringPattern {
  description: string;
  transactions: Transaction[];
  averageAmount: number;
  frequency: 'weekly' | 'monthly' | 'quarterly' | 'yearly' | 'irregular';
  daysBetween: number[];
  confidence: number;
}

export function analyzeRecurringExpenses(transactions: Transaction[]): RecurringPattern[] {
  // Filter to only expenses (negative amounts)
  const expenses = transactions.filter(t => t.amount < 0);
  
  // Group similar transactions by description similarity
  const groups = groupSimilarTransactions(expenses);
  
  // Analyze each group for recurring patterns
  const patterns: RecurringPattern[] = [];
  
  for (const group of groups) {
    if (group.length >= 2) { // Need at least 2 transactions to detect pattern
      const pattern = analyzeTransactionGroup(group);
      if (pattern.confidence > 0.3) { // Only include patterns with reasonable confidence
        patterns.push(pattern);
      }
    }
  }
  
  return patterns.sort((a, b) => b.confidence - a.confidence);
}

function groupSimilarTransactions(transactions: Transaction[]): Transaction[][] {
  const groups: Transaction[][] = [];
  const used = new Set<string>();
  
  for (const transaction of transactions) {
    if (used.has(transaction.id)) continue;
    
    const group = [transaction];
    used.add(transaction.id);
    
    // Find similar transactions
    for (const other of transactions) {
      if (used.has(other.id)) continue;
      
      if (isSimilarTransaction(transaction, other)) {
        group.push(other);
        used.add(other.id);
      }
    }
    
    groups.push(group);
  }
  
  return groups;
}

function isSimilarTransaction(t1: Transaction, t2: Transaction): boolean {
  // Check if descriptions are similar
  const desc1 = normalizeDescription(t1.description);
  const desc2 = normalizeDescription(t2.description);
  
  // Calculate similarity score
  const similarity = calculateStringSimilarity(desc1, desc2);
  
  // Check if amounts are similar (within 20% difference)
  const amountDiff = Math.abs(t1.amount - t2.amount) / Math.abs(t1.amount);
  
  return similarity > 0.6 && amountDiff < 0.2;
}

function normalizeDescription(description: string): string {
  return description
    .toLowerCase()
    .replace(/\d+/g, '') // Remove numbers
    .replace(/[^\w\s]/g, ' ') // Replace special chars with spaces
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim();
}

function calculateStringSimilarity(str1: string, str2: string): number {
  const words1 = new Set(str1.split(' '));
  const words2 = new Set(str2.split(' '));
  
  const intersection = new Set([...words1].filter(x => words2.has(x)));
  const union = new Set([...words1, ...words2]);
  
  return intersection.size / union.size;
}

function analyzeTransactionGroup(transactions: Transaction[]): RecurringPattern {
  // Sort by date
  const sorted = transactions.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  
  // Calculate days between transactions
  const daysBetween: number[] = [];
  for (let i = 1; i < sorted.length; i++) {
    const days = Math.round(
      (new Date(sorted[i].date).getTime() - new Date(sorted[i - 1].date).getTime()) / 
      (1000 * 60 * 60 * 24)
    );
    daysBetween.push(days);
  }
  
  // Calculate average amount
  const averageAmount = Math.abs(
    transactions.reduce((sum, t) => sum + t.amount, 0) / transactions.length
  );
  
  // Determine frequency and confidence
  const { frequency, confidence } = determineFrequency(daysBetween);
  
  return {
    description: transactions[0].description,
    transactions: sorted,
    averageAmount,
    frequency,
    daysBetween,
    confidence
  };
}

function determineFrequency(daysBetween: number[]): { frequency: 'weekly' | 'monthly' | 'quarterly' | 'yearly' | 'irregular', confidence: number } {
  if (daysBetween.length === 0) {
    return { frequency: 'irregular', confidence: 0 };
  }
  
  const avgDays = daysBetween.reduce((sum, days) => sum + days, 0) / daysBetween.length;
  const variance = daysBetween.reduce((sum, days) => sum + Math.pow(days - avgDays, 2), 0) / daysBetween.length;
  const stdDev = Math.sqrt(variance);
  
  // Lower standard deviation = higher confidence
  const baseConfidence = Math.max(0, 1 - (stdDev / avgDays));
  
  // Determine frequency based on average days
  if (avgDays >= 5 && avgDays <= 9) {
    return { frequency: 'weekly', confidence: baseConfidence };
  } else if (avgDays >= 25 && avgDays <= 35) {
    return { frequency: 'monthly', confidence: baseConfidence };
  } else if (avgDays >= 85 && avgDays <= 95) {
    return { frequency: 'quarterly', confidence: baseConfidence };
  } else if (avgDays >= 350 && avgDays <= 380) {
    return { frequency: 'yearly', confidence: baseConfidence };
  } else {
    return { frequency: 'irregular', confidence: baseConfidence * 0.5 };
  }
}

export function predictNextPayments(pattern: RecurringPattern): { date: string; amount: number; confidence: number }[] {
  const lastTransaction = pattern.transactions[pattern.transactions.length - 1];
  const lastDate = new Date(lastTransaction.date);
  
  const predictions: { date: string; amount: number; confidence: number }[] = [];
  
  // Calculate average interval
  const avgInterval = pattern.daysBetween.reduce((sum, days) => sum + days, 0) / pattern.daysBetween.length;
  
  // Generate 3 predictions
  for (let i = 1; i <= 3; i++) {
    const nextDate = new Date(lastDate);
    nextDate.setDate(nextDate.getDate() + Math.round(avgInterval * i));
    
    // Confidence decreases with distance into future
    const confidence = pattern.confidence * Math.pow(0.8, i - 1);
    
    predictions.push({
      date: nextDate.toISOString().split('T')[0],
      amount: pattern.averageAmount,
      confidence
    });
  }
  
  return predictions;
}

export function generateFriendlyName(description: string): string {
  const normalized = normalizeDescription(description);
  const words = normalized.split(' ').filter(word => word.length > 2);
  
  // Take the first 2-3 meaningful words and capitalize them
  const meaningfulWords = words.slice(0, 3);
  return meaningfulWords
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

export function extractKeywords(description: string): string[] {
  const normalized = normalizeDescription(description);
  const words = normalized.split(' ').filter(word => word.length > 2);
  
  // Return unique words that could be useful as keywords
  return [...new Set(words)].slice(0, 5);
}