import React, { useState, useEffect } from 'react';
import { Transaction, TrackedExpense } from '../types';
import { storage } from '../utils/storage';
import { 
  analyzeRecurringExpenses, 
  predictNextPayments, 
  generateFriendlyName, 
  extractKeywords 
} from '../utils/trackingAnalysis';
import { 
  Clock, 
  Plus, 
  Edit2, 
  Trash2, 
  Check, 
  X, 
  Calendar, 
  DollarSign, 
  TrendingUp, 
  AlertCircle, 
  Loader2,
  RefreshCw,
  Eye,
  EyeOff,
  Target,
  Settings
} from 'lucide-react';

export default function TrackingTab() {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [trackedExpenses, setTrackedExpenses] = useState<TrackedExpense[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingName, setEditingName] = useState('');
  const [editingKeywords, setEditingKeywords] = useState('');
  const [showInactive, setShowInactive] = useState(false);
  const [showManualAdd, setShowManualAdd] = useState(false);
  const [manualName, setManualName] = useState('');
  const [manualKeywords, setManualKeywords] = useState('');
  const [manualFrequency, setManualFrequency] = useState<'weekly' | 'monthly' | 'quarterly' | 'yearly'>('monthly');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const [transactionsData, trackedData] = await Promise.all([
        storage.getTransactions(),
        storage.getTrackedExpenses()
      ]);

      setTransactions(transactionsData);
      setTrackedExpenses(trackedData);
    } catch (err) {
      console.error('Failed to load tracking data:', err);
      setError('Failed to load tracking data');
    } finally {
      setIsLoading(false);
    }
  };

  const analyzeExpenses = async () => {
    try {
      setIsAnalyzing(true);
      setError(null);

      // Analyze transactions for recurring patterns
      const patterns = analyzeRecurringExpenses(transactions);
      
      // Get existing tracked expenses to avoid duplicates
      const existing = await storage.getTrackedExpenses();
      const existingDescriptions = new Set(
        existing.flatMap(te => te.keywords)
      );

      // Create new tracked expenses from patterns
      const newTrackedExpenses: TrackedExpense[] = [];
      
      for (const pattern of patterns) {
        // Check if this pattern is already being tracked
        const keywords = extractKeywords(pattern.description);
        const isAlreadyTracked = keywords.some(keyword => 
          existingDescriptions.has(keyword.toLowerCase())
        );

        if (!isAlreadyTracked && pattern.transactions.length >= 2) {
          const friendlyName = generateFriendlyName(pattern.description);
          const predictions = predictNextPayments(pattern);
          
          const lastThreeCharges = pattern.transactions
            .slice(-3)
            .map(t => ({
              date: t.date,
              amount: Math.abs(t.amount),
              description: t.description
            }));

          const newExpense: Omit<TrackedExpense, 'id' | 'createdAt' | 'updatedAt'> = {
            friendlyName,
            keywords,
            lastThreeCharges,
            predictedPayments: predictions,
            averageAmount: pattern.averageAmount,
            frequency: pattern.frequency,
            isActive: true
          };

          const savedExpense = await storage.addTrackedExpense(newExpense);
          newTrackedExpenses.push(savedExpense);
        }
      }

      // Refresh the tracked expenses list
      await loadData();

      if (newTrackedExpenses.length > 0) {
        setError(null);
      } else {
        setError('No new recurring patterns found. Try adding more transaction data.');
      }
    } catch (err) {
      console.error('Failed to analyze expenses:', err);
      setError('Failed to analyze recurring expenses');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleManualAdd = async () => {
    if (!manualName.trim() || !manualKeywords.trim()) {
      setError('Please provide both a friendly name and keywords');
      return;
    }

    try {
      const keywords = manualKeywords
        .split(',')
        .map(k => k.trim().toLowerCase())
        .filter(k => k.length > 0);

      // Find matching transactions based on keywords
      const matchingTransactions = transactions.filter(t => 
        t.amount < 0 && // Only expenses
        keywords.some(keyword => 
          t.description.toLowerCase().includes(keyword)
        )
      );

      let lastThreeCharges: { date: string; amount: number; description: string }[] = [];
      let averageAmount = 0;
      let predictedPayments: { date: string; amount: number; confidence: number }[] = [];

      if (matchingTransactions.length > 0) {
        // Use actual transaction data
        lastThreeCharges = matchingTransactions
          .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
          .slice(0, 3)
          .map(t => ({
            date: t.date,
            amount: Math.abs(t.amount),
            description: t.description
          }));

        averageAmount = matchingTransactions.reduce((sum, t) => sum + Math.abs(t.amount), 0) / matchingTransactions.length;

        // Generate predictions if we have enough data
        if (matchingTransactions.length >= 2) {
          const pattern = {
            transactions: matchingTransactions.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()),
            averageAmount,
            frequency: manualFrequency,
            confidence: 0.6, // Lower confidence for manual entries
            daysBetween: [],
            description: manualName
          };

          // Calculate days between transactions
          for (let i = 1; i < pattern.transactions.length; i++) {
            const days = Math.round(
              (new Date(pattern.transactions[i].date).getTime() - new Date(pattern.transactions[i - 1].date).getTime()) / 
              (1000 * 60 * 60 * 24)
            );
            pattern.daysBetween.push(days);
          }

          predictedPayments = predictNextPayments(pattern);
        } else {
          // Generate basic predictions based on frequency
          const today = new Date();
          const intervalDays = manualFrequency === 'weekly' ? 7 : 
                              manualFrequency === 'monthly' ? 30 : 
                              manualFrequency === 'quarterly' ? 90 : 365;

          for (let i = 1; i <= 3; i++) {
            const nextDate = new Date(today);
            nextDate.setDate(nextDate.getDate() + (intervalDays * i));
            
            predictedPayments.push({
              date: nextDate.toISOString().split('T')[0],
              amount: averageAmount || 50, // Default amount if no transactions found
              confidence: 0.4 // Lower confidence for estimated predictions
            });
          }
        }
      } else {
        // No matching transactions found - create placeholder data
        averageAmount = 50; // Default amount
        const today = new Date();
        const intervalDays = manualFrequency === 'weekly' ? 7 : 
                            manualFrequency === 'monthly' ? 30 : 
                            manualFrequency === 'quarterly' ? 90 : 365;

        for (let i = 1; i <= 3; i++) {
          const nextDate = new Date(today);
          nextDate.setDate(nextDate.getDate() + (intervalDays * i));
          
          predictedPayments.push({
            date: nextDate.toISOString().split('T')[0],
            amount: averageAmount,
            confidence: 0.3 // Low confidence for estimated predictions
          });
        }
      }

      const newExpense: Omit<TrackedExpense, 'id' | 'createdAt' | 'updatedAt'> = {
        friendlyName: manualName.trim(),
        keywords,
        lastThreeCharges,
        predictedPayments,
        averageAmount,
        frequency: manualFrequency,
        isActive: true
      };

      await storage.addTrackedExpense(newExpense);
      await loadData();

      // Reset form
      setManualName('');
      setManualKeywords('');
      setManualFrequency('monthly');
      setShowManualAdd(false);
      setError(null);
    } catch (err) {
      console.error('Failed to add manual expense:', err);
      setError('Failed to add manual tracked expense');
    }
  };

  const updateTrackedExpenseData = async (expenseId: string) => {
    try {
      const expense = trackedExpenses.find(te => te.id === expenseId);
      if (!expense) return;

      // Find matching transactions based on keywords
      const matchingTransactions = transactions.filter(t => 
        t.amount < 0 && // Only expenses
        expense.keywords.some(keyword => 
          t.description.toLowerCase().includes(keyword.toLowerCase())
        )
      );

      if (matchingTransactions.length > 0) {
        // Update last three charges
        const lastThreeCharges = matchingTransactions
          .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
          .slice(0, 3)
          .map(t => ({
            date: t.date,
            amount: Math.abs(t.amount),
            description: t.description
          }));

        // Recalculate average amount
        const averageAmount = matchingTransactions.reduce((sum, t) => sum + Math.abs(t.amount), 0) / matchingTransactions.length;

        // Generate new predictions if we have enough data
        let predictedPayments = expense.predictedPayments;
        if (matchingTransactions.length >= 2) {
          // Create a pattern object for prediction
          const pattern = {
            transactions: matchingTransactions.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()),
            averageAmount,
            frequency: expense.frequency,
            confidence: 0.7,
            daysBetween: [],
            description: expense.friendlyName
          };

          // Calculate days between transactions
          for (let i = 1; i < pattern.transactions.length; i++) {
            const days = Math.round(
              (new Date(pattern.transactions[i].date).getTime() - new Date(pattern.transactions[i - 1].date).getTime()) / 
              (1000 * 60 * 60 * 24)
            );
            pattern.daysBetween.push(days);
          }

          predictedPayments = predictNextPayments(pattern);
        }

        await storage.updateTrackedExpense(expenseId, {
          lastThreeCharges,
          averageAmount,
          predictedPayments
        });

        await loadData();
      }
    } catch (err) {
      console.error('Failed to update tracked expense:', err);
      setError('Failed to update tracked expense data');
    }
  };

  const handleStartEdit = (expense: TrackedExpense) => {
    setEditingId(expense.id);
    setEditingName(expense.friendlyName);
    setEditingKeywords(expense.keywords.join(', '));
  };

  const handleSaveEdit = async () => {
    if (!editingId) return;

    try {
      const keywords = editingKeywords
        .split(',')
        .map(k => k.trim().toLowerCase())
        .filter(k => k.length > 0);

      await storage.updateTrackedExpense(editingId, {
        friendlyName: editingName.trim(),
        keywords
      });

      // Update the data for this expense
      await updateTrackedExpenseData(editingId);

      setEditingId(null);
      setEditingName('');
      setEditingKeywords('');
    } catch (err) {
      console.error('Failed to save edit:', err);
      setError('Failed to save changes');
    }
  };

  const handleCancelEdit = () => {
    setEditingId(null);
    setEditingName('');
    setEditingKeywords('');
  };

  const handleToggleActive = async (expenseId: string) => {
    try {
      const expense = trackedExpenses.find(te => te.id === expenseId);
      if (expense) {
        await storage.updateTrackedExpense(expenseId, {
          isActive: !expense.isActive
        });
        await loadData();
      }
    } catch (err) {
      console.error('Failed to toggle active status:', err);
      setError('Failed to update expense status');
    }
  };

  const handleRemoveExpense = async (expenseId: string) => {
    if (confirm('Are you sure you want to remove this tracked expense?')) {
      try {
        await storage.removeTrackedExpense(expenseId);
        await loadData();
      } catch (err) {
        console.error('Failed to remove expense:', err);
        setError('Failed to remove tracked expense');
      }
    }
  };

  const getFrequencyColor = (frequency: string) => {
    switch (frequency) {
      case 'weekly': return 'text-blue-400';
      case 'monthly': return 'text-green-400';
      case 'quarterly': return 'text-yellow-400';
      case 'yearly': return 'text-purple-400';
      default: return 'text-gray-400';
    }
  };

  const getFrequencyIcon = (frequency: string) => {
    switch (frequency) {
      case 'weekly': return <Calendar className="text-blue-400" size={16} />;
      case 'monthly': return <Calendar className="text-green-400" size={16} />;
      case 'quarterly': return <Calendar className="text-yellow-400" size={16} />;
      case 'yearly': return <Calendar className="text-purple-400" size={16} />;
      default: return <Clock className="text-gray-400" size={16} />;
    }
  };

  const activeExpenses = trackedExpenses.filter(te => te.isActive);
  const inactiveExpenses = trackedExpenses.filter(te => !te.isActive);
  const displayExpenses = showInactive ? trackedExpenses : activeExpenses;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-400 mx-auto mb-4" />
          <p className="text-white">Loading tracking data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Expense Tracking</h1>
          <p className="text-gray-400 mt-1">
            AI-powered recurring expense detection and payment predictions
          </p>
        </div>
        
        <div className="flex space-x-3">
          <button
            onClick={() => setShowManualAdd(!showManualAdd)}
            className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
          >
            <Plus size={16} />
            <span>Add Manual</span>
          </button>
          <button
            onClick={() => setShowInactive(!showInactive)}
            className="flex items-center space-x-2 px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
          >
            {showInactive ? <EyeOff size={16} /> : <Eye size={16} />}
            <span>{showInactive ? 'Hide Inactive' : 'Show All'}</span>
          </button>
          <button
            onClick={analyzeExpenses}
            disabled={isAnalyzing || transactions.length === 0}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
          >
            {isAnalyzing ? <Loader2 size={16} className="animate-spin" /> : <RefreshCw size={16} />}
            <span>{isAnalyzing ? 'Analyzing...' : 'Analyze Expenses'}</span>
          </button>
        </div>
      </div>

      {error && (
        <div className="flex items-center space-x-2 p-4 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400">
          <AlertCircle size={20} />
          <span>{error}</span>
        </div>
      )}

      {/* Manual Add Form */}
      {showManualAdd && (
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-medium text-white mb-4 flex items-center space-x-2">
            <Settings size={20} />
            <span>Add Manual Tracked Expense</span>
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm text-gray-400 mb-2">Friendly Name</label>
              <input
                type="text"
                value={manualName}
                onChange={(e) => setManualName(e.target.value)}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="e.g., Netflix Subscription, Car Insurance"
              />
            </div>
            
            <div>
              <label className="block text-sm text-gray-400 mb-2">Frequency</label>
              <select
                value={manualFrequency}
                onChange={(e) => setManualFrequency(e.target.value as 'weekly' | 'monthly' | 'quarterly' | 'yearly')}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
                <option value="quarterly">Quarterly</option>
                <option value="yearly">Yearly</option>
              </select>
            </div>
          </div>
          
          <div className="mb-4">
            <label className="block text-sm text-gray-400 mb-2">Keywords (comma-separated)</label>
            <input
              type="text"
              value={manualKeywords}
              onChange={(e) => setManualKeywords(e.target.value)}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., netflix, streaming, subscription"
            />
            <p className="text-xs text-gray-500 mt-1">
              Keywords help match transactions. Use words that appear in transaction descriptions.
            </p>
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={handleManualAdd}
              disabled={!manualName.trim() || !manualKeywords.trim()}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
            >
              <Check size={16} />
              <span>Add Expense</span>
            </button>
            <button
              onClick={() => {
                setShowManualAdd(false);
                setManualName('');
                setManualKeywords('');
                setManualFrequency('monthly');
              }}
              className="flex items-center space-x-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
            >
              <X size={16} />
              <span>Cancel</span>
            </button>
          </div>
        </div>
      )}

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-blue-500/20 rounded-lg">
              <Target className="text-blue-400" size={24} />
            </div>
            <div>
              <p className="text-gray-400 text-sm">Active Tracked</p>
              <p className="text-2xl font-bold text-white">{activeExpenses.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-green-500/20 rounded-lg">
              <DollarSign className="text-green-400" size={24} />
            </div>
            <div>
              <p className="text-gray-400 text-sm">Monthly Total</p>
              <p className="text-2xl font-bold text-white">
                ${activeExpenses
                  .filter(te => te.frequency === 'monthly')
                  .reduce((sum, te) => sum + te.averageAmount, 0)
                  .toFixed(2)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-yellow-500/20 rounded-lg">
              <Calendar className="text-yellow-400" size={24} />
            </div>
            <div>
              <p className="text-gray-400 text-sm">Next 7 Days</p>
              <p className="text-2xl font-bold text-white">
                {activeExpenses.reduce((count, te) => {
                  const nextWeek = new Date();
                  nextWeek.setDate(nextWeek.getDate() + 7);
                  return count + te.predictedPayments.filter(p => 
                    new Date(p.date) <= nextWeek
                  ).length;
                }, 0)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-purple-500/20 rounded-lg">
              <TrendingUp className="text-purple-400" size={24} />
            </div>
            <div>
              <p className="text-gray-400 text-sm">Avg Confidence</p>
              <p className="text-2xl font-bold text-white">
                {activeExpenses.length > 0 
                  ? Math.round(
                      activeExpenses.reduce((sum, te) => 
                        sum + (te.predictedPayments[0]?.confidence || 0), 0
                      ) / activeExpenses.length * 100
                    )
                  : 0}%
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Tracked Expenses */}
      {displayExpenses.length === 0 ? (
        <div className="bg-gray-800 rounded-lg p-8 text-center">
          <Clock className="mx-auto h-16 w-16 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-white mb-2">No Tracked Expenses</h3>
          <p className="text-gray-400 mb-4">
            {transactions.length === 0 
              ? 'Upload transaction data first, then analyze to detect recurring expenses or add manual tracking'
              : 'Click "Analyze Expenses" to automatically detect patterns or "Add Manual" to create custom tracking'
            }
          </p>
          <div className="flex justify-center space-x-3">
            <button
              onClick={() => setShowManualAdd(true)}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
            >
              <Plus size={16} />
              <span>Add Manual</span>
            </button>
            {transactions.length > 0 && (
              <button
                onClick={analyzeExpenses}
                disabled={isAnalyzing}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white rounded-lg transition-colors"
              >
                {isAnalyzing ? <Loader2 size={16} className="animate-spin" /> : <RefreshCw size={16} />}
                <span>{isAnalyzing ? 'Analyzing...' : 'Analyze Expenses'}</span>
              </button>
            )}
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {displayExpenses.map((expense) => (
            <div key={expense.id} className={`bg-gray-800 rounded-lg p-6 ${!expense.isActive ? 'opacity-60' : ''}`}>
              {editingId === expense.id ? (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm text-gray-400 mb-2">Friendly Name</label>
                    <input
                      type="text"
                      value={editingName}
                      onChange={(e) => setEditingName(e.target.value)}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter a friendly name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm text-gray-400 mb-2">Keywords (comma-separated)</label>
                    <input
                      type="text"
                      value={editingKeywords}
                      onChange={(e) => setEditingKeywords(e.target.value)}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="keyword1, keyword2, keyword3"
                    />
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={handleSaveEdit}
                      className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
                    >
                      <Check size={16} />
                      <span>Save</span>
                    </button>
                    <button
                      onClick={handleCancelEdit}
                      className="flex items-center space-x-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
                    >
                      <X size={16} />
                      <span>Cancel</span>
                    </button>
                  </div>
                </div>
              ) : (
                <>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-2">
                        {getFrequencyIcon(expense.frequency)}
                        <h3 className="text-lg font-medium text-white">{expense.friendlyName}</h3>
                        {!expense.isActive && (
                          <span className="px-2 py-1 bg-gray-600 text-gray-300 rounded-full text-xs">
                            Inactive
                          </span>
                        )}
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs ${getFrequencyColor(expense.frequency)} bg-gray-700`}>
                        {expense.frequency}
                      </span>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => updateTrackedExpenseData(expense.id)}
                        className="p-2 text-gray-400 hover:text-blue-400 transition-colors"
                        title="Refresh data"
                      >
                        <RefreshCw size={16} />
                      </button>
                      <button
                        onClick={() => handleToggleActive(expense.id)}
                        className={`p-2 transition-colors ${
                          expense.isActive 
                            ? 'text-gray-400 hover:text-yellow-400' 
                            : 'text-yellow-400 hover:text-white'
                        }`}
                        title={expense.isActive ? 'Deactivate' : 'Activate'}
                      >
                        {expense.isActive ? <EyeOff size={16} /> : <Eye size={16} />}
                      </button>
                      <button
                        onClick={() => handleStartEdit(expense)}
                        className="p-2 text-gray-400 hover:text-white transition-colors"
                        title="Edit name and keywords"
                      >
                        <Edit2 size={16} />
                      </button>
                      <button
                        onClick={() => handleRemoveExpense(expense.id)}
                        className="p-2 text-gray-400 hover:text-red-400 transition-colors"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Keywords */}
                    <div>
                      <h4 className="text-sm font-medium text-gray-400 mb-2">Keywords</h4>
                      <div className="flex flex-wrap gap-2">
                        {expense.keywords.map((keyword, index) => (
                          <span key={index} className="px-2 py-1 bg-blue-500/20 text-blue-300 rounded-full text-xs">
                            {keyword}
                          </span>
                        ))}
                      </div>
                      <p className="text-xs text-gray-500 mt-2">
                        Avg: ${expense.averageAmount.toFixed(2)}
                      </p>
                    </div>

                    {/* Last 3 Charges */}
                    <div>
                      <h4 className="text-sm font-medium text-gray-400 mb-2">Last 3 Charges</h4>
                      <div className="space-y-2">
                        {expense.lastThreeCharges.length > 0 ? (
                          expense.lastThreeCharges.map((charge, index) => (
                            <div key={index} className="flex justify-between text-sm">
                              <span className="text-gray-300">
                                {new Date(charge.date).toLocaleDateString()}
                              </span>
                              <span className="text-red-400">${charge.amount.toFixed(2)}</span>
                            </div>
                          ))
                        ) : (
                          <p className="text-xs text-gray-500">No matching transactions found</p>
                        )}
                      </div>
                    </div>

                    {/* Next 3 Predictions */}
                    <div>
                      <h4 className="text-sm font-medium text-gray-400 mb-2">Predicted Payments</h4>
                      <div className="space-y-2">
                        {expense.predictedPayments.map((prediction, index) => (
                          <div key={index} className="flex justify-between text-sm">
                            <div className="flex items-center space-x-2">
                              <span className="text-gray-300">
                                {new Date(prediction.date).toLocaleDateString()}
                              </span>
                              <span className="text-xs text-gray-500">
                                ({Math.round(prediction.confidence * 100)}%)
                              </span>
                            </div>
                            <span className="text-yellow-400">${prediction.amount.toFixed(2)}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Instructions */}
      <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
        <h4 className="text-blue-300 font-medium mb-2">How Expense Tracking Works</h4>
        <ul className="text-sm text-blue-200 space-y-1">
          <li>• <strong>Auto Analysis:</strong> AI analyzes transaction history to identify recurring payment patterns</li>
          <li>• <strong>Manual Addition:</strong> Add custom tracked expenses with keywords and frequency settings</li>
          <li>• <strong>Smart Matching:</strong> Keywords help match future transactions to tracked expenses</li>
          <li>• <strong>Predictions:</strong> Shows last 3 charges and predicts next 3 payment dates with confidence scores</li>
          <li>• <strong>Easy Management:</strong> Edit friendly names and keywords, refresh data, or deactivate tracking</li>
          <li>• <strong>Flexible Tracking:</strong> Works with both automatically detected and manually added expenses</li>
        </ul>
      </div>
    </div>
  );
}