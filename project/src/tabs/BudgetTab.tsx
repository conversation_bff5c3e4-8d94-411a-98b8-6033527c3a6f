import React, { useState, useEffect } from 'react';
import { BudgetCategory, Transaction } from '../types';
import { storage } from '../utils/storage';
import { calculateIncomeProjections, calculateOverallIncomeProjection } from '../utils/incomeProjections';
import CategoryManager from '../components/CategoryManager';
import { 
  DollarSign, 
  Save, 
  RotateCcw, 
  TrendingUp, 
  TrendingDown, 
  Settings, 
  Loader2,
  Calendar,
  Target,
  PiggyBank
} from 'lucide-react';

export default function BudgetTab() {
  const [categories, setCategories] = useState<BudgetCategory[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [originalCategories, setOriginalCategories] = useState<BudgetCategory[]>([]);
  const [hasChanges, setHasChanges] = useState(false);
  const [showCategoryManager, setShowCategoryManager] = useState(false);
  const [activeTab, setActiveTab] = useState<'expense' | 'income'>('expense');
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadCategories();
  }, []);

  useEffect(() => {
    const hasChanged = JSON.stringify(categories) !== JSON.stringify(originalCategories);
    setHasChanges(hasChanged);
  }, [categories, originalCategories]);

  const loadCategories = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const [loadedCategories, loadedTransactions] = await Promise.all([
        storage.getBudgetCategories(),
        storage.getTransactions()
      ]);
      
      setTransactions(loadedTransactions);
      
      // Calculate current spending/income for each category
      const updatedCategories = loadedCategories.map(category => ({
        ...category,
        currentSpending: category.type === 'expense' 
          ? loadedTransactions
              .filter(t => t.category === category.name && t.amount < 0)
              .reduce((sum, t) => sum + Math.abs(t.amount), 0)
          : loadedTransactions
              .filter(t => t.category === category.name && t.amount > 0)
              .reduce((sum, t) => sum + t.amount, 0)
      }));
      
      setCategories(updatedCategories);
      setOriginalCategories(JSON.parse(JSON.stringify(updatedCategories)));
    } catch (err) {
      console.error('Failed to load categories:', err);
      setError('Failed to load budget categories');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAmountChange = (index: number, amount: string) => {
    const newCategories = [...categories];
    newCategories[index].budgetAmount = parseFloat(amount) || 0;
    setCategories(newCategories);
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      setError(null);
      
      await storage.saveBudgetCategories(categories);
      setOriginalCategories(JSON.parse(JSON.stringify(categories)));
      setHasChanges(false);
    } catch (err) {
      console.error('Failed to save categories:', err);
      setError('Failed to save budget categories');
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = () => {
    setCategories(JSON.parse(JSON.stringify(originalCategories)));
    setHasChanges(false);
  };

  const handleAddCategory = async (name: string, type: 'expense' | 'income') => {
    try {
      await storage.addBudgetCategory(name, type);
      await loadCategories();
    } catch (err) {
      console.error('Failed to add category:', err);
      setError('Failed to add category');
    }
  };

  const handleRemoveCategory = async (categoryId: string) => {
    try {
      await storage.removeBudgetCategory(categoryId);
      await loadCategories();
    } catch (err) {
      console.error('Failed to remove category:', err);
      setError('Failed to remove category');
    }
  };

  const handleUpdateCategoryName = async (categoryId: string, newName: string) => {
    try {
      await storage.updateBudgetCategoryName(categoryId, newName);
      await loadCategories();
    } catch (err) {
      console.error('Failed to update category name:', err);
      setError('Failed to update category name');
    }
  };

  const expenseCategories = categories.filter(cat => cat.type === 'expense');
  const incomeCategories = categories.filter(cat => cat.type === 'income');
  const displayCategories = activeTab === 'expense' ? expenseCategories : incomeCategories;

  const getTotalBudget = (type: 'expense' | 'income') => {
    return categories
      .filter(cat => cat.type === type)
      .reduce((sum, cat) => sum + cat.budgetAmount, 0);
  };

  const getTotalActual = (type: 'expense' | 'income') => {
    return categories
      .filter(cat => cat.type === type)
      .reduce((sum, cat) => sum + cat.currentSpending, 0);
  };

  const getNetIncome = () => {
    return getTotalActual('income') - getTotalActual('expense');
  };

  const getBudgetedNet = () => {
    return getTotalBudget('income') - getTotalBudget('expense');
  };

  // Calculate income projections
  const incomeProjections = calculateIncomeProjections(transactions, incomeCategories);
  const overallProjection = calculateOverallIncomeProjection(transactions);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-400 mx-auto mb-4" />
          <p className="text-white">Loading budget categories...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Budget Management</h1>
          <p className="text-gray-400 mt-1">
            Set monthly budget limits and income targets
          </p>
        </div>
        
        <div className="flex space-x-3">
          <button
            onClick={() => setShowCategoryManager(!showCategoryManager)}
            className="flex items-center space-x-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
          >
            <Settings size={16} />
            <span>Manage Categories</span>
          </button>
          <button
            onClick={handleReset}
            disabled={!hasChanges}
            className="flex items-center space-x-2 px-4 py-2 bg-gray-700 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg transition-colors"
          >
            <RotateCcw size={16} />
            <span>Reset</span>
          </button>
          <button
            onClick={handleSave}
            disabled={!hasChanges || isSaving}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
          >
            {isSaving ? <Loader2 size={16} className="animate-spin" /> : <Save size={16} />}
            <span>{isSaving ? 'Saving...' : 'Save Changes'}</span>
          </button>
        </div>
      </div>

      {error && (
        <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400">
          {error}
        </div>
      )}

      {/* Category Manager */}
      {showCategoryManager && (
        <CategoryManager
          categories={categories}
          onAddCategory={handleAddCategory}
          onRemoveCategory={handleRemoveCategory}
          onUpdateCategoryName={handleUpdateCategoryName}
        />
      )}

      {/* Budget Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-green-500/20 rounded-lg">
              <DollarSign className="text-green-400" size={24} />
            </div>
            <div>
              <p className="text-gray-400 text-sm">Total Income</p>
              <p className="text-2xl font-bold text-green-400">${getTotalActual('income').toFixed(2)}</p>
              <p className="text-xs text-gray-500">Target: ${getTotalBudget('income').toFixed(2)}</p>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-red-500/20 rounded-lg">
              <TrendingUp className="text-red-400" size={24} />
            </div>
            <div>
              <p className="text-gray-400 text-sm">Total Expenses</p>
              <p className="text-2xl font-bold text-red-400">${getTotalActual('expense').toFixed(2)}</p>
              <p className="text-xs text-gray-500">Budget: ${getTotalBudget('expense').toFixed(2)}</p>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center space-x-3">
            <div className={`p-3 rounded-lg ${getNetIncome() >= 0 ? 'bg-green-500/20' : 'bg-red-500/20'}`}>
              {getNetIncome() >= 0 ? (
                <PiggyBank className="text-green-400" size={24} />
              ) : (
                <TrendingDown className="text-red-400" size={24} />
              )}
            </div>
            <div>
              <p className="text-gray-400 text-sm">Net Income</p>
              <p className={`text-2xl font-bold ${getNetIncome() >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                ${getNetIncome().toFixed(2)}
              </p>
              <p className="text-xs text-gray-500">Budgeted: ${getBudgetedNet().toFixed(2)}</p>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-blue-500/20 rounded-lg">
              <Calendar className="text-blue-400" size={24} />
            </div>
            <div>
              <p className="text-gray-400 text-sm">Daily Income Rate</p>
              <p className="text-2xl font-bold text-blue-400">${overallProjection.dailyRate.toFixed(2)}</p>
              <p className="text-xs text-gray-500">Annual: ${overallProjection.annualProjected.toFixed(0)}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Income Projections */}
      {activeTab === 'income' && incomeCategories.length > 0 && (
        <div className="bg-gray-800 rounded-lg p-6">
          <h2 className="text-lg font-medium text-white mb-4">Income Projections</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {incomeCategories.map((category) => {
              const projection = incomeProjections[category.name];
              if (!projection) return null;

              return (
                <div key={category.id} className="bg-gray-700 rounded-lg p-4">
                  <h3 className="font-medium text-white mb-2">{category.name}</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Received to date:</span>
                      <span className="text-green-400">${projection.actualToDate.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Daily rate:</span>
                      <span className="text-white">${projection.dailyRate.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Monthly projected:</span>
                      <span className="text-blue-400">${projection.monthlyProjected.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Annual projected:</span>
                      <span className="text-purple-400">${projection.annualProjected.toFixed(0)}</span>
                    </div>
                    <div className="pt-2 border-t border-gray-600">
                      <div className="flex justify-between text-xs">
                        <span className="text-gray-500">Days elapsed:</span>
                        <span className="text-gray-400">{projection.daysElapsed} / {projection.daysInMonth}</span>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-gray-800 rounded-lg p-1">
        <button
          onClick={() => setActiveTab('expense')}
          className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-colors ${
            activeTab === 'expense'
              ? 'bg-red-600 text-white'
              : 'text-gray-400 hover:text-white hover:bg-gray-700'
          }`}
        >
          <TrendingUp size={16} />
          <span>Expenses ({expenseCategories.length})</span>
        </button>
        <button
          onClick={() => setActiveTab('income')}
          className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-colors ${
            activeTab === 'income'
              ? 'bg-green-600 text-white'
              : 'text-gray-400 hover:text-white hover:bg-gray-700'
          }`}
        >
          <Target size={16} />
          <span>Income ({incomeCategories.length})</span>
        </button>
      </div>

      {/* Budget Categories */}
      <div className="bg-gray-800 rounded-lg p-6">
        <h2 className="text-lg font-medium text-white mb-6">
          {activeTab === 'expense' ? 'Expense Budget' : 'Income Targets'}
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {displayCategories.map((category, index) => {
            const percentage = category.budgetAmount > 0 
              ? (category.currentSpending / category.budgetAmount) * 100 
              : 0;
            const isOverBudget = category.type === 'expense' 
              ? category.currentSpending > category.budgetAmount
              : category.currentSpending < category.budgetAmount;
            
            return (
              <div key={category.id} className="bg-gray-700 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-medium text-white">{category.name}</h3>
                  <div className="flex items-center space-x-2">
                    {category.type === 'expense' ? (
                      isOverBudget ? (
                        <TrendingUp className="text-red-400" size={16} />
                      ) : (
                        <TrendingDown className="text-green-400" size={16} />
                      )
                    ) : (
                      isOverBudget ? (
                        <TrendingDown className="text-red-400" size={16} />
                      ) : (
                        <TrendingUp className="text-green-400" size={16} />
                      )
                    )}
                  </div>
                </div>
                
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm text-gray-400 mb-1">
                      Monthly {category.type === 'expense' ? 'Budget' : 'Target'}
                    </label>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">$</span>
                      <input
                        type="number"
                        value={category.budgetAmount}
                        onChange={(e) => handleAmountChange(
                          categories.findIndex(c => c.id === category.id), 
                          e.target.value
                        )}
                        className="w-full pl-8 pr-3 py-2 bg-gray-600 border border-gray-500 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                        min="0"
                        step="0.01"
                      />
                    </div>
                  </div>
                  
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">
                      {category.type === 'expense' ? 'Spent:' : 'Received:'}
                    </span>
                    <span className={
                      category.type === 'expense' 
                        ? (isOverBudget ? 'text-red-400' : 'text-gray-300')
                        : (isOverBudget ? 'text-red-400' : 'text-green-400')
                    }>
                      ${category.currentSpending.toFixed(2)}
                    </span>
                  </div>
                  
                  <div className="w-full bg-gray-600 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all ${
                        category.type === 'expense'
                          ? (isOverBudget ? 'bg-red-500' : 'bg-green-500')
                          : (isOverBudget ? 'bg-red-500' : 'bg-green-500')
                      }`}
                      style={{ width: `${Math.min(percentage, 100)}%` }}
                    />
                  </div>
                  
                  <div className="flex justify-between text-xs">
                    <span className="text-gray-500">
                      {percentage.toFixed(1)}% {category.type === 'expense' ? 'used' : 'achieved'}
                    </span>
                    <span className={`${
                      category.type === 'expense'
                        ? (isOverBudget ? 'text-red-400' : 'text-gray-400')
                        : (isOverBudget ? 'text-red-400' : 'text-gray-400')
                    }`}>
                      ${Math.abs(category.budgetAmount - category.currentSpending).toFixed(2)} 
                      {category.type === 'expense' 
                        ? (isOverBudget ? ' over' : ' remaining')
                        : (isOverBudget ? ' short' : ' over target')
                      }
                    </span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}