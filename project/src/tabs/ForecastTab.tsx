import React, { useState, useEffect } from 'react';
import { Transaction, ForecastData } from '../types';
import { storage } from '../utils/storage';
import { generateForecast, calculateTrend } from '../utils/forecast';
import { TrendingUp, TrendingDown, Calendar, AlertTriangle, Loader2 } from 'lucide-react';

export default function ForecastTab() {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [forecast, setForecast] = useState<ForecastData[]>([]);
  const [selectedTimeframe, setSelectedTimeframe] = useState<3 | 6>(6);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const loadedTransactions = await storage.getTransactions();
      setTransactions(loadedTransactions);
      
      const forecastData = generateForecast(loadedTransactions);
      setForecast(forecastData);
    } catch (err) {
      console.error('Failed to load forecast data:', err);
      setError('Failed to load forecast data');
    } finally {
      setIsLoading(false);
    }
  };

  const getMonthlySpending = () => {
    const monthlyData = transactions.reduce((acc, transaction) => {
      if (transaction.amount >= 0) return acc; // Skip income
      
      const date = new Date(transaction.date);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      
      if (!acc[monthKey]) {
        acc[monthKey] = 0;
      }
      acc[monthKey] += Math.abs(transaction.amount);
      
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(monthlyData)
      .map(([monthKey, spending]) => {
        const [year, month] = monthKey.split('-');
        return {
          monthKey,
          year: parseInt(year),
          month: parseInt(month),
          spending
        };
      })
      .sort((a, b) => a.year - b.year || a.month - b.month);
  };

  const getCurrentMonthTrend = () => {
    const monthlySpending = getMonthlySpending();
    if (monthlySpending.length < 2) return null;

    const current = monthlySpending[monthlySpending.length - 1];
    const previous = monthlySpending[monthlySpending.length - 2];
    
    return calculateTrend(current.spending, previous.spending);
  };

  const getAverageMonthlySpending = () => {
    const monthlySpending = getMonthlySpending();
    if (monthlySpending.length === 0) return 0;
    
    const total = monthlySpending.reduce((sum, month) => sum + month.spending, 0);
    return total / monthlySpending.length;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-400 mx-auto mb-4" />
          <p className="text-white">Loading forecast data...</p>
        </div>
      </div>
    );
  }

  const displayForecast = forecast.slice(0, selectedTimeframe);
  const monthlySpending = getMonthlySpending();
  const trend = getCurrentMonthTrend();
  const averageSpending = getAverageMonthlySpending();

  if (transactions.length === 0) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-white">Spending Forecast</h1>
          <p className="text-gray-400 mt-1">
            Project your future spending based on historical data
          </p>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-8 text-center">
          <AlertTriangle className="mx-auto h-16 w-16 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-white mb-2">No Data Available</h3>
          <p className="text-gray-400">
            Upload transaction data to generate spending forecasts
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Spending Forecast</h1>
          <p className="text-gray-400 mt-1">
            AI-powered predictions based on your spending patterns
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <label className="text-sm text-gray-400">Forecast period:</label>
          <select
            value={selectedTimeframe}
            onChange={(e) => setSelectedTimeframe(Number(e.target.value) as 3 | 6)}
            className="px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value={3}>3 months</option>
            <option value={6}>6 months</option>
          </select>
        </div>
      </div>

      {error && (
        <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400">
          {error}
        </div>
      )}

      {/* Current Trends */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-blue-500/20 rounded-lg">
              <Calendar className="text-blue-400" size={24} />
            </div>
            <div>
              <p className="text-gray-400 text-sm">Average Monthly</p>
              <p className="text-2xl font-bold text-white">${averageSpending.toFixed(2)}</p>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-purple-500/20 rounded-lg">
              <TrendingUp className="text-purple-400" size={24} />
            </div>
            <div>
              <p className="text-gray-400 text-sm">Data Points</p>
              <p className="text-2xl font-bold text-white">{monthlySpending.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center space-x-3">
            <div className={`p-3 rounded-lg ${
              trend?.direction === 'up' ? 'bg-red-500/20' : 
              trend?.direction === 'down' ? 'bg-green-500/20' : 'bg-gray-500/20'
            }`}>
              {trend?.direction === 'up' ? (
                <TrendingUp className="text-red-400" size={24} />
              ) : trend?.direction === 'down' ? (
                <TrendingDown className="text-green-400" size={24} />
              ) : (
                <TrendingUp className="text-gray-400" size={24} />
              )}
            </div>
            <div>
              <p className="text-gray-400 text-sm">Monthly Trend</p>
              <p className={`text-lg font-bold ${
                trend?.direction === 'up' ? 'text-red-400' : 
                trend?.direction === 'down' ? 'text-green-400' : 'text-gray-400'
              }`}>
                {trend ? `${trend.percentage.toFixed(1)}%` : 'N/A'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Forecast Table */}
      <div className="bg-gray-800 rounded-lg p-6">
        <h2 className="text-lg font-medium text-white mb-6">Projected Spending</h2>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="text-gray-400 border-b border-gray-700">
                <th className="text-left py-3">Month</th>
                <th className="text-right py-3">Projected Amount</th>
                <th className="text-right py-3">Confidence</th>
                <th className="text-right py-3">vs. Average</th>
              </tr>
            </thead>
            <tbody>
              {displayForecast.map((forecast, index) => {
                const vsAverage = ((forecast.projectedSpending - averageSpending) / averageSpending) * 100;
                
                return (
                  <tr key={index} className="border-b border-gray-700/50">
                    <td className="py-4">
                      <div className="text-white font-medium">
                        {forecast.month} {forecast.year}
                      </div>
                    </td>
                    <td className="py-4 text-right">
                      <span className="text-white font-medium text-lg">
                        ${forecast.projectedSpending.toFixed(2)}
                      </span>
                    </td>
                    <td className="py-4 text-right">
                      <div className="flex items-center justify-end space-x-2">
                        <div className="w-16 bg-gray-600 rounded-full h-2">
                          <div
                            className="h-2 bg-blue-500 rounded-full"
                            style={{ width: `${forecast.confidence * 100}%` }}
                          />
                        </div>
                        <span className="text-sm text-gray-400 w-12">
                          {(forecast.confidence * 100).toFixed(0)}%
                        </span>
                      </div>
                    </td>
                    <td className="py-4 text-right">
                      <span className={`flex items-center justify-end space-x-1 ${
                        vsAverage > 0 ? 'text-red-400' : 'text-green-400'
                      }`}>
                        {vsAverage > 0 ? (
                          <TrendingUp size={16} />
                        ) : (
                          <TrendingDown size={16} />
                        )}
                        <span>{Math.abs(vsAverage).toFixed(1)}%</span>
                      </span>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Historical Context */}
      <div className="bg-gray-800 rounded-lg p-6">
        <h2 className="text-lg font-medium text-white mb-6">Historical Spending</h2>
        
        <div className="space-y-3">
          {monthlySpending.slice(-6).map((month) => (
            <div key={month.monthKey} className="flex items-center justify-between py-2">
              <span className="text-gray-300">
                {new Date(month.year, month.month - 1).toLocaleString('default', { 
                  month: 'long',
                  year: 'numeric' 
                })}
              </span>
              <span className="text-white font-medium">${month.spending.toFixed(2)}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}