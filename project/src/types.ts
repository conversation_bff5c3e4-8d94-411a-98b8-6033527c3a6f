export interface Transaction {
  id: string;
  date: string;
  description: string;
  category: string;
  amount: number;
  originalData?: string;
}

export interface BudgetCategory {
  id: string;
  name: string;
  budgetAmount: number;
  currentSpending: number;
  type: 'expense' | 'income';
}

export interface IncomeProjection {
  dailyRate: number;
  monthlyProjected: number;
  annualProjected: number;
  daysInMonth: number;
  daysElapsed: number;
  actualToDate: number;
}

export interface MonthlyHistory {
  month: string;
  year: number;
  totalSpent: number;
  totalIncome: number;
  netIncome: number;
  transactions: Transaction[];
  categories: Record<string, number>;
  incomeCategories: Record<string, number>;
}

export interface ForecastData {
  month: string;
  year: number;
  projectedSpending: number;
  projectedIncome: number;
  projectedNet: number;
  confidence: number;
}

export interface KeywordMapping {
  id: string;
  keyword: string;
  categoryName: string;
  createdAt: string;
}

export interface TrackedExpense {
  id: string;
  friendlyName: string;
  keywords: string[];
  lastThreeCharges: {
    date: string;
    amount: number;
    description: string;
  }[];
  predictedPayments: {
    date: string;
    amount: number;
    confidence: number;
  }[];
  averageAmount: number;
  frequency: 'weekly' | 'monthly' | 'quarterly' | 'yearly' | 'irregular';
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface LicenseInfo {
  isLicensed: boolean;
  email: string;
  licenseKey: string;
  activatedAt?: string;
  expiresAt?: string;
  features: {
    unlimitedData: boolean;
    forecastAccess: boolean;
    extendedTracking: boolean;
    fullHistory: boolean;
  };
}

export type OpenAIModel = 
  // Chat models
  | 'gpt-3.5-turbo' 
  | 'gpt-3.5-turbo-0125' 
  | 'gpt-4' 
  | 'gpt-4-0125-preview' 
  | 'gpt-4-turbo-preview' 
  | 'gpt-4-turbo' 
  | 'gpt-4-1106-preview'
  // Completion models
  | 'gpt-3.5-turbo-instruct' 
  | 'text-davinci-003' 
  | 'text-davinci-002';

export interface OpenAIRequest {
  input_csv: string;
  instruction: string;
  model?: OpenAIModel;
}

export interface OpenAIResponse {
  categorized: Array<{
    date: string;
    description: string;
    category: string;
    amount: number;
  }>;
}

export type TabType = 'spending' | 'budget' | 'forecast' | 'history' | 'admin' | 'data' | 'tracking';