import React, { useState, useEffect } from 'react';
import { LicenseInfo } from '../types';
import { licenseManager } from '../utils/licenseManager';
import { 
  Shield, 
  ShieldCheck, 
  ShieldX, 
  Key, 
  Mail, 
  Calendar, 
  AlertTriangle, 
  CheckCircle, 
  Loader2,
  Copy,
  Eye,
  EyeOff,
  Refresh<PERSON><PERSON>,
  Clock
} from 'lucide-react';

interface LicenseManagerProps {
  licenseInfo: LicenseInfo;
  onLicenseUpdate: () => void;
}

export default function LicenseManager({ licenseInfo, onLicenseUpdate }: LicenseManagerProps) {
  const [email, setEmail] = useState(licenseInfo.email || '');
  const [licenseKey, setLicenseKey] = useState(licenseInfo.licenseKey || '');
  const [showLicenseKey, setShowLicenseKey] = useState(false);
  const [isActivating, setIsActivating] = useState(false);
  const [isDeactivating, setIsDeactivating] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [demoTimeRemaining, setDemoTimeRemaining] = useState<{ hours: number; expired: boolean } | null>(null);

  // Update demo time remaining every minute
  useEffect(() => {
    const updateDemoTime = () => {
      const timeRemaining = licenseManager.getDemoActivationTimeRemaining();
      setDemoTimeRemaining(timeRemaining);
    };

    updateDemoTime();
    const interval = setInterval(updateDemoTime, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [licenseInfo]);

  const handleActivate = async () => {
    if (!email.trim() || !licenseKey.trim()) {
      setMessage({ type: 'error', text: 'Please enter both email and license key' });
      return;
    }

    setIsActivating(true);
    setMessage(null);

    try {
      const result = await licenseManager.activateLicense(email.trim(), licenseKey.trim());
      
      if (result.success) {
        setMessage({ type: 'success', text: result.message });
        onLicenseUpdate();
      } else {
        setMessage({ type: 'error', text: result.message });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to activate license. Please try again.' });
    } finally {
      setIsActivating(false);
    }
  };

  const handleDeactivate = async () => {
    if (confirm('Are you sure you want to deactivate your license? This will restrict access to premium features.')) {
      setIsDeactivating(true);
      setMessage(null);
      
      try {
        await licenseManager.deactivateLicense();
        setEmail('');
        setLicenseKey('');
        setMessage({ type: 'success', text: 'License deactivated successfully' });
        onLicenseUpdate();
      } catch (error) {
        setMessage({ type: 'error', text: 'Failed to deactivate license' });
      } finally {
        setIsDeactivating(false);
      }
    }
  };

  const generateDemoKey = () => {
    const demoEmail = '<EMAIL>';
    const demoKey = licenseManager.generateDemoLicenseKey(demoEmail);
    setEmail(demoEmail);
    setLicenseKey(demoKey);
    setMessage({ type: 'success', text: 'Demo license key generated with 36-hour activation window. Click "Activate License" to use it.' });
  };

  const copyDemoKey = () => {
    const demoKey = licenseManager.generateDemoLicenseKey('<EMAIL>');
    navigator.clipboard.writeText(demoKey);
    setMessage({ type: 'success', text: 'Demo license key copied to clipboard' });
  };

  const isLicensed = licenseManager.isLicensed();
  const statusMessage = licenseManager.getLicenseStatusMessage();
  const statusColor = licenseManager.getLicenseStatusColor();

  return (
    <div className="bg-gray-800 rounded-lg p-6">
      <div className="flex items-center space-x-3 mb-6">
        <div className={`p-3 rounded-lg ${isLicensed ? 'bg-green-500/20' : 'bg-red-500/20'}`}>
          {isLicensed ? (
            <ShieldCheck className="text-green-400" size={24} />
          ) : (
            <ShieldX className="text-red-400" size={24} />
          )}
        </div>
        <div>
          <h2 className="text-lg font-medium text-white">License Management</h2>
          <p className={`text-sm ${statusColor}`}>{statusMessage}</p>
        </div>
      </div>

      {/* Demo License Warning */}
      {demoTimeRemaining && !demoTimeRemaining.expired && (
        <div className={`flex items-center space-x-2 p-3 rounded-lg mb-4 ${
          demoTimeRemaining.hours <= 12 
            ? 'bg-red-500/10 border border-red-500/20 text-red-400'
            : demoTimeRemaining.hours <= 24
            ? 'bg-yellow-500/10 border border-yellow-500/20 text-yellow-400'
            : 'bg-orange-500/10 border border-orange-500/20 text-orange-400'
        }`}>
          <Clock size={16} />
          <div className="text-sm">
            <p className="font-medium">Demo License Active</p>
            <p>
              {demoTimeRemaining.hours} hours remaining before data is automatically cleared. 
              Purchase a paid license to prevent data loss.
            </p>
          </div>
        </div>
      )}

      {message && (
        <div className={`flex items-center space-x-2 p-3 rounded-lg mb-4 ${
          message.type === 'success' 
            ? 'bg-green-500/10 border border-green-500/20 text-green-400' 
            : 'bg-red-500/10 border border-red-500/20 text-red-400'
        }`}>
          {message.type === 'success' ? <CheckCircle size={16} /> : <AlertTriangle size={16} />}
          <span className="text-sm">{message.text}</span>
        </div>
      )}

      {isLicensed ? (
        <div className="space-y-4">
          {/* Licensed Status */}
          <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-3">
              <ShieldCheck className="text-green-400" size={20} />
              <span className="text-green-400 font-medium">Licensed</span>
              {demoTimeRemaining && (
                <span className="px-2 py-1 bg-orange-500/20 text-orange-300 rounded-full text-xs">
                  Demo
                </span>
              )}
            </div>
            
            <div className="space-y-2 text-sm">
              <div className="flex items-center space-x-2">
                <Mail className="text-gray-400" size={16} />
                <span className="text-gray-300">Email: {licenseInfo.email}</span>
              </div>
              
              {licenseInfo.activatedAt && (
                <div className="flex items-center space-x-2">
                  <Calendar className="text-gray-400" size={16} />
                  <span className="text-gray-300">
                    Activated: {new Date(licenseInfo.activatedAt).toLocaleDateString()}
                  </span>
                </div>
              )}
              
              {licenseInfo.expiresAt && (
                <div className="flex items-center space-x-2">
                  <Calendar className="text-gray-400" size={16} />
                  <span className="text-gray-300">
                    Expires: {new Date(licenseInfo.expiresAt).toLocaleDateString()}
                  </span>
                </div>
              )}

              {demoTimeRemaining && (
                <div className="flex items-center space-x-2">
                  <Clock className="text-orange-400" size={16} />
                  <span className="text-orange-300">
                    Data clearing in: {demoTimeRemaining.hours} hours
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Features */}
          <div className="bg-gray-700 rounded-lg p-4">
            <h4 className="text-white font-medium mb-3">Licensed Features</h4>
            <div className="grid grid-cols-2 gap-3">
              <div className="flex items-center space-x-2">
                <CheckCircle className="text-green-400" size={16} />
                <span className="text-sm text-gray-300">Unlimited Data Upload</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="text-green-400" size={16} />
                <span className="text-sm text-gray-300">Forecast Access</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="text-green-400" size={16} />
                <span className="text-sm text-gray-300">Extended Tracking</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="text-green-400" size={16} />
                <span className="text-sm text-gray-300">Full History Access</span>
              </div>
            </div>
          </div>

          {/* Data Clearing Warning for Demo */}
          {demoTimeRemaining && (
            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <AlertTriangle className="text-red-400" size={16} />
                <span className="text-red-400 font-medium text-sm">Demo License Data Policy</span>
              </div>
              <p className="text-red-300 text-sm">
                At the end of the 36-hour activation window, all data will be automatically cleared 
                upon next access if a paid license has not been purchased. This includes transactions, 
                budget categories, history, and all settings.
              </p>
            </div>
          )}

          {/* Deactivate Button */}
          <button
            onClick={handleDeactivate}
            disabled={isDeactivating}
            className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
          >
            {isDeactivating ? <Loader2 size={16} className="animate-spin" /> : <ShieldX size={16} />}
            <span>{isDeactivating ? 'Deactivating...' : 'Deactivate License'}</span>
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          {/* Limitations Warning */}
          <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-3">
              <AlertTriangle className="text-yellow-400" size={20} />
              <span className="text-yellow-400 font-medium">Unlicensed Limitations</span>
            </div>
            
            <ul className="text-sm text-yellow-200 space-y-1">
              <li>• Data upload limited to 3 months</li>
              <li>• History tracking capped at 3 months</li>
              <li>• Forecast feature disabled</li>
              <li>• Expense tracking limited to 1 month</li>
            </ul>
          </div>

          {/* License Key Info */}
          <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Clock className="text-blue-400" size={16} />
              <span className="text-blue-300 font-medium text-sm">License Key Information</span>
            </div>
            <div className="text-xs text-blue-200 space-y-1">
              <p>• Your license key is assigned to your registered email and can only be activated once</p>
              <p>• License keys have a 36-hour activation period from time of issue</p>
              <p>• If activation period expires, the license requires reissuing</p>
              <p>• Demo licenses will clear all data after the 36-hour window expires</p>
            </div>
          </div>

          {/* Demo Key Helper */}
          <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-blue-300 font-medium text-sm">Demo License Key</span>
              <div className="flex space-x-2">
                <button
                  onClick={generateDemoKey}
                  className="flex items-center space-x-1 px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-xs transition-colors"
                >
                  <RefreshCw size={12} />
                  <span>Generate</span>
                </button>
                <button
                  onClick={copyDemoKey}
                  className="flex items-center space-x-1 px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-xs transition-colors"
                >
                  <Copy size={12} />
                  <span>Copy</span>
                </button>
              </div>
            </div>
            <p className="text-xs text-blue-200">
              Generate a demo license key with 36-hour activation window. Data will be cleared when the window expires.
            </p>
          </div>

          {/* License Activation Form */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">
                Registered Email
              </label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full pl-10 pr-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter your registered email"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">
                License Key (Encrypted)
              </label>
              <div className="relative">
                <Key className="absolute left-3 top-3 text-gray-400" size={16} />
                <textarea
                  value={licenseKey}
                  onChange={(e) => setLicenseKey(e.target.value)}
                  className="w-full pl-10 pr-10 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-xs resize-none"
                  placeholder="Paste your encrypted license key here (256+ characters)"
                  rows={4}
                />
                <button
                  type="button"
                  onClick={() => setShowLicenseKey(!showLicenseKey)}
                  className="absolute right-3 top-3 text-gray-400 hover:text-white"
                >
                  {showLicenseKey ? <EyeOff size={16} /> : <Eye size={16} />}
                </button>
              </div>
              <div className="flex justify-between items-center mt-1">
                <p className="text-xs text-gray-500">
                  Encrypted license key with email, expiry, and activation period
                </p>
                <span className="text-xs text-gray-500">
                  {licenseKey.length}/256+ chars
                </span>
              </div>
            </div>

            <button
              onClick={handleActivate}
              disabled={isActivating || !email.trim() || !licenseKey.trim()}
              className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
            >
              {isActivating ? (
                <Loader2 size={16} className="animate-spin" />
              ) : (
                <Shield size={16} />
              )}
              <span>{isActivating ? 'Activating...' : 'Activate License'}</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
}