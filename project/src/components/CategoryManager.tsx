import React, { useState } from 'react';
import { BudgetCategory } from '../types';
import { Plus, Edit2, Trash2, Check, X, DollarSign, TrendingUp } from 'lucide-react';

interface CategoryManagerProps {
  categories: BudgetCategory[];
  onAddCategory: (name: string, type: 'expense' | 'income') => void;
  onRemoveCategory: (categoryId: string) => void;
  onUpdateCategoryName: (categoryId: string, newName: string) => void;
}

export default function CategoryManager({ 
  categories, 
  onAddCategory, 
  onRemoveCategory, 
  onUpdateCategoryName 
}: CategoryManagerProps) {
  const [isAdding, setIsAdding] = useState(false);
  const [newCategoryName, setNewCategoryName] = useState('');
  const [newCategoryType, setNewCategoryType] = useState<'expense' | 'income'>('expense');
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingName, setEditingName] = useState('');
  const [activeTab, setActiveTab] = useState<'expense' | 'income'>('expense');

  const handleAddCategory = () => {
    if (newCategoryName.trim()) {
      onAddCategory(newCategoryName.trim(), newCategoryType);
      setNewCategoryName('');
      setNewCategoryType('expense');
      setIsAdding(false);
    }
  };

  const handleStartEdit = (category: BudgetCategory) => {
    setEditingId(category.id);
    setEditingName(category.name);
  };

  const handleSaveEdit = () => {
    if (editingId && editingName.trim()) {
      onUpdateCategoryName(editingId, editingName.trim());
      setEditingId(null);
      setEditingName('');
    }
  };

  const handleCancelEdit = () => {
    setEditingId(null);
    setEditingName('');
  };

  const handleRemoveCategory = (categoryId: string) => {
    const categoryToRemove = categories.find(cat => cat.id === categoryId);
    const categoriesOfSameType = categories.filter(cat => cat.type === categoryToRemove?.type);
    
    if (categoriesOfSameType.length <= 1) {
      alert(`You must have at least one ${categoryToRemove?.type} category`);
      return;
    }
    
    if (confirm('Are you sure you want to remove this category? All transactions in this category will be moved to "Miscellaneous".')) {
      onRemoveCategory(categoryId);
    }
  };

  const expenseCategories = categories.filter(cat => cat.type === 'expense');
  const incomeCategories = categories.filter(cat => cat.type === 'income');
  const displayCategories = activeTab === 'expense' ? expenseCategories : incomeCategories;

  return (
    <div className="bg-gray-800 rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-medium text-white">Manage Categories</h2>
        <button
          onClick={() => {
            setIsAdding(true);
            setNewCategoryType(activeTab);
          }}
          className="flex items-center space-x-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
        >
          <Plus size={16} />
          <span>Add {activeTab === 'expense' ? 'Expense' : 'Income'}</span>
        </button>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-gray-700 rounded-lg p-1 mb-6">
        <button
          onClick={() => setActiveTab('expense')}
          className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-colors ${
            activeTab === 'expense'
              ? 'bg-red-600 text-white'
              : 'text-gray-400 hover:text-white hover:bg-gray-600'
          }`}
        >
          <TrendingUp size={16} />
          <span>Expenses ({expenseCategories.length})</span>
        </button>
        <button
          onClick={() => setActiveTab('income')}
          className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-colors ${
            activeTab === 'income'
              ? 'bg-green-600 text-white'
              : 'text-gray-400 hover:text-white hover:bg-gray-600'
          }`}
        >
          <DollarSign size={16} />
          <span>Income ({incomeCategories.length})</span>
        </button>
      </div>

      <div className="space-y-3">
        {displayCategories.map((category) => (
          <div key={category.id} className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
            {editingId === category.id ? (
              <div className="flex items-center space-x-2 flex-1">
                <input
                  type="text"
                  value={editingName}
                  onChange={(e) => setEditingName(e.target.value)}
                  className="flex-1 px-3 py-1 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  onKeyPress={(e) => e.key === 'Enter' && handleSaveEdit()}
                  autoFocus
                />
                <button
                  onClick={handleSaveEdit}
                  className="p-1 text-green-400 hover:text-green-300"
                >
                  <Check size={16} />
                </button>
                <button
                  onClick={handleCancelEdit}
                  className="p-1 text-red-400 hover:text-red-300"
                >
                  <X size={16} />
                </button>
              </div>
            ) : (
              <>
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-white font-medium">{category.name}</span>
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      category.type === 'income' 
                        ? 'bg-green-500/20 text-green-300' 
                        : 'bg-red-500/20 text-red-300'
                    }`}>
                      {category.type}
                    </span>
                  </div>
                  <p className="text-sm text-gray-400">
                    {category.type === 'income' ? 'Target' : 'Budget'}: ${category.budgetAmount.toFixed(2)} | 
                    {category.type === 'income' ? 'Received' : 'Spent'}: ${category.currentSpending.toFixed(2)}
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleStartEdit(category)}
                    className="p-2 text-gray-400 hover:text-white transition-colors"
                  >
                    <Edit2 size={16} />
                  </button>
                  <button
                    onClick={() => handleRemoveCategory(category.id)}
                    className="p-2 text-gray-400 hover:text-red-400 transition-colors"
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
              </>
            )}
          </div>
        ))}

        {isAdding && (
          <div className="flex items-center space-x-2 p-3 bg-gray-700 rounded-lg">
            <input
              type="text"
              placeholder={`Enter ${newCategoryType} category name`}
              value={newCategoryName}
              onChange={(e) => setNewCategoryName(e.target.value)}
              className="flex-1 px-3 py-1 bg-gray-600 border border-gray-500 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              onKeyPress={(e) => e.key === 'Enter' && handleAddCategory()}
              autoFocus
            />
            <select
              value={newCategoryType}
              onChange={(e) => setNewCategoryType(e.target.value as 'expense' | 'income')}
              className="px-3 py-1 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="expense">Expense</option>
              <option value="income">Income</option>
            </select>
            <button
              onClick={handleAddCategory}
              className="p-1 text-green-400 hover:text-green-300"
            >
              <Check size={16} />
            </button>
            <button
              onClick={() => {
                setIsAdding(false);
                setNewCategoryName('');
                setNewCategoryType('expense');
              }}
              className="p-1 text-red-400 hover:text-red-300"
            >
              <X size={16} />
            </button>
          </div>
        )}
      </div>

      <div className="mt-4 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
        <p className="text-sm text-blue-300">
          <strong>Note:</strong> Income categories help track different sources of income and set monthly targets. 
          Changes to category names will automatically update all existing transactions and historical data.
        </p>
      </div>
    </div>
  );
}