import React from 'react';
import { TabType } from '../types';
import { 
  CreditCard, 
  DollarSign, 
  TrendingUp, 
  History, 
  Settings, 
  Download,
  Clock
} from 'lucide-react';

interface TabNavigationProps {
  activeTab: TabType;
  onTabChange: (tab: TabType) => void;
}

const tabs = [
  { id: 'spending' as TabType, label: 'Spending', icon: CreditCard },
  { id: 'budget' as TabType, label: 'Budget', icon: DollarSign },
  { id: 'tracking' as TabType, label: 'Tracking', icon: Clock },
  { id: 'forecast' as TabType, label: 'Forecast', icon: TrendingUp },
  { id: 'history' as TabType, label: 'History', icon: History },
  { id: 'admin' as TabType, label: 'Admin', icon: Settings },
  { id: 'data' as TabType, label: 'Data', icon: Download },
];

export default function TabNavigation({ activeTab, onTabChange }: TabNavigationProps) {
  return (
    <div className="border-b border-gray-700">
      <nav className="flex space-x-8">
        {tabs.map((tab) => {
          const Icon = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={`
                flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors
                ${activeTab === tab.id
                  ? 'border-blue-500 text-blue-400'
                  : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                }
              `}
            >
              <Icon size={18} />
              <span>{tab.label}</span>
            </button>
          );
        })}
      </nav>
    </div>
  );
}