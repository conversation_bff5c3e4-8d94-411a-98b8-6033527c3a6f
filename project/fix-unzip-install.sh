#!/bin/bash

# Quick fix to install unzip before package extraction

REMOTE_HOST="**********"
REMOTE_USER="labmaster"
REMOTE_TARGET="${REMOTE_USER}@${REMOTE_HOST}"

echo "Installing unzip and other essential tools on remote server..."

ssh -t "${REMOTE_TARGET}" "
  echo 'Updating package lists...'
  sudo apt-get update
  
  echo 'Installing essential tools...'
  sudo apt-get install -y unzip curl wget net-tools
  
  echo 'Installing Node.js if not present...'
  if ! command -v node >/dev/null 2>&1; then
    curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
    sudo apt-get install -y nodejs
  fi
  
  echo 'Installing Nginx if not present...'
  if ! command -v nginx >/dev/null 2>&1; then
    sudo apt-get install -y nginx
  fi
  
  echo 'Verifying tools installation:'
  command -v unzip && echo '✓ unzip installed' || echo '✗ unzip failed'
  command -v curl && echo '✓ curl installed' || echo '✗ curl failed'
  command -v wget && echo '✓ wget installed' || echo '✗ wget failed'
  command -v node && echo '✓ node installed' || echo '✗ node failed'
  command -v npm && echo '✓ npm installed' || echo '✗ npm failed'
  command -v nginx && echo '✓ nginx installed' || echo '✗ nginx failed'
  command -v systemctl && echo '✓ systemctl available' || echo '✗ systemctl failed'
"

echo "Remote dependencies installation completed!"

# Now find the staging directory and extract the package
echo "Finding staging directory and extracting package..."

STAGING_DIR=$(ssh "${REMOTE_TARGET}" "ls -td /tmp/focusbudget-staging-* 2>/dev/null | head -1")

if [ -n "$STAGING_DIR" ]; then
  echo "Found staging directory: $STAGING_DIR"
  
  ssh "${REMOTE_TARGET}" "
    cd '$STAGING_DIR'
    echo 'Current directory contents:'
    ls -la
    
    if [ -f package.zip ]; then
      echo 'Extracting package.zip with unzip...'
      unzip -o package.zip
      rm package.zip
      
      echo 'Extraction complete. Contents:'
      ls -la
      
      echo 'Verifying essential files:'
      for file in package.json server.cjs; do
        if [ -f \"\$file\" ]; then
          echo \"✓ \$file found\"
        else
          echo \"✗ \$file missing\"
        fi
      done
    else
      echo 'No package.zip found in staging directory'
    fi
  "
else
  echo "No staging directory found"
fi

echo "Fix completed! You can now re-run the deployment script or it should continue automatically."
