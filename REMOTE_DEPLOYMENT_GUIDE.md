# FocusBudget Remote Deployment Guide

## Overview
The `deploy-to-remote.sh` script provides a complete automated deployment solution for Focus<PERSON>udget to your remote server at `**********`. It supports both SSH key authentication (recommended) and password authentication.

## Quick Start

### Option 1: Run with Interactive Setup
```bash
./deploy-to-remote.sh
```
The script will guide you through authentication setup and deployment.

### Option 2: Pre-setup SSH Keys (Recommended)
```bash
# Generate SSH key (if you don't have one)
ssh-keygen -t ed25519 -C "<EMAIL>"

# Copy key to remote server
ssh-copy-id labmaster@**********

# Run deployment
./deploy-to-remote.sh
```

## Authentication Methods

### SSH Key Authentication (Recommended)
**Advantages:**
- More secure
- No password prompts during deployment
- Faster execution
- Industry standard for automation

**Setup:**
1. The script can generate SSH keys for you
2. Or use existing keys in `~/.ssh/`
3. Automatically copies keys to remote server
4. Tests connection before proceeding

### Password Authentication
**When to use:**
- Quick one-time deployments
- SSH keys not feasible in your environment
- Testing purposes

**Requirements:**
- <PERSON><PERSON><PERSON> will install `sshpass` if needed
- You'll be prompted for password once
- Password is used for all SSH operations during deployment

## What the Script Does

### 1. **Pre-deployment Checks**
- Validates local project structure
- Tests SSH connectivity
- Sets up authentication method
- Checks remote server requirements

### 2. **Backup & Safety**
- Creates timestamped backup of existing deployment
- Backs up data directory (`/var/lib/focusbudget`)
- Stops running services safely

### 3. **Code Transfer**
- Syncs entire project to remote server
- Excludes unnecessary files (node_modules, .git, etc.)
- Uses efficient rsync with progress display

### 4. **Secure Deployment**
- Runs `build-and-deploy-secure.sh` on remote server
- Creates dedicated `focusbudget_user`
- Sets up proper directory structure:
  - `/opt/focusbudget/` - Application files
  - `/var/lib/focusbudget/` - Data directory
  - `/var/log/focusbudget/` - Log directory
- Applies security hardening
- Configures systemd service
- Sets up Nginx reverse proxy

### 5. **Verification**
- Tests service status
- Checks application endpoints
- Displays recent logs
- Confirms deployment success

### 6. **Cleanup**
- Removes temporary files
- Provides management commands

## Troubleshooting

### SSH Connection Issues
```bash
# Test SSH connection manually
ssh labmaster@**********

# If connection fails, check:
# 1. Server is accessible: ping **********
# 2. SSH service is running on remote server
# 3. Firewall allows SSH (port 22)
# 4. Username 'labmaster' exists on remote server
```

### SSH Key Issues
```bash
# Check if you have SSH keys
ls -la ~/.ssh/

# Generate new key if needed
ssh-keygen -t ed25519 -C "<EMAIL>"

# Copy key to server (will prompt for password)
ssh-copy-id labmaster@**********

# Test key authentication
ssh -o BatchMode=yes labmaster@********** "echo 'Key auth works'"
```

### Password Authentication Issues
```bash
# Install sshpass if needed
sudo apt-get install sshpass  # Ubuntu/Debian
sudo yum install sshpass      # RHEL/CentOS
sudo dnf install sshpass      # Fedora

# Test password authentication
sshpass -p 'your_password' ssh labmaster@********** "echo 'Password auth works'"
```

### Deployment Failures
```bash
# Check remote server logs
ssh labmaster@********** "sudo journalctl -u focusbudget --since '10 minutes ago'"

# Check service status
ssh labmaster@********** "sudo systemctl status focusbudget"

# Check application logs
ssh labmaster@********** "sudo tail -f /var/log/focusbudget/*.log"
```

## Post-Deployment Management

### Service Management
```bash
# Check status
ssh labmaster@********** "sudo systemctl status focusbudget"

# Restart service
ssh labmaster@********** "sudo systemctl restart focusbudget"

# View logs
ssh labmaster@********** "sudo journalctl -u focusbudget -f"
```

### Application Access
- **Primary URL:** http://**********
- **Health Check:** http://**********/health
- **API Status:** http://**********/api/status

### File Locations on Remote Server
- **Application:** `/opt/focusbudget/`
- **Data:** `/var/lib/focusbudget/`
- **Logs:** `/var/log/focusbudget/`
- **Service:** `/etc/systemd/system/focusbudget.service`
- **Nginx Config:** `/etc/nginx/sites-available/focusbudget`

## Security Features

The deployment includes comprehensive security hardening:
- Dedicated system user with no shell access
- Restricted file permissions
- Systemd security sandbox
- Network access restrictions
- Nginx rate limiting
- Security headers

## Backup and Recovery

### Automatic Backups
Each deployment creates a backup in `/tmp/focusbudget-backup-TIMESTAMP/`

### Manual Backup
```bash
ssh labmaster@********** "
  sudo mkdir -p /tmp/manual-backup-$(date +%Y%m%d)
  sudo cp -r /opt/focusbudget /tmp/manual-backup-$(date +%Y%m%d)/
  sudo cp -r /var/lib/focusbudget /tmp/manual-backup-$(date +%Y%m%d)/
"
```

### Restore from Backup
```bash
ssh labmaster@********** "
  sudo systemctl stop focusbudget
  sudo cp -r /tmp/focusbudget-backup-TIMESTAMP/opt/focusbudget/* /opt/focusbudget/
  sudo cp -r /tmp/focusbudget-backup-TIMESTAMP/data-backup/* /var/lib/focusbudget/
  sudo systemctl start focusbudget
"
```

## Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review the deployment logs
3. Verify network connectivity
4. Ensure proper permissions on remote server

The script provides detailed output and error messages to help diagnose issues.
