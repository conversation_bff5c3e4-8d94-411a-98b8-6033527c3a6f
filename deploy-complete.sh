#!/bin/bash

# FocusBudget Complete Deployment Script
# This script handles the complete end-to-end deployment process

# Exit on error
set -e

# Colors for better readability
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

# Configuration
REMOTE_HOST="**********"
REMOTE_USER="labmaster"
REMOTE_TARGET="${REMOTE_USER}@${REMOTE_HOST}"
LOCAL_PROJECT_DIR="/BudgetPage/project"
APP_NAME="focusbudget"
APP_USER="${APP_NAME}_user"
APP_GROUP="${APP_NAME}_group"
APP_ROOT="/opt/${APP_NAME}"
DATA_ROOT="/var/lib/${APP_NAME}"
LOG_ROOT="/var/log/${APP_NAME}"

echo -e "\n${BLUE}==============================="
echo -e " FocusBudget Complete Deployment"
echo -e "===============================${NC}\n"

echo -e "${BLUE}Configuration:${NC}"
echo -e "${YELLOW}• Remote server: ${REMOTE_HOST}${NC}"
echo -e "${YELLOW}• Remote user: ${REMOTE_USER}${NC}"
echo -e "${YELLOW}• Application: ${APP_NAME}${NC}"
echo -e "${YELLOW}• Service user: ${APP_USER}${NC}"
echo -e "${YELLOW}• App directory: ${APP_ROOT}${NC}"
echo -e "${YELLOW}• Data directory: ${DATA_ROOT}${NC}"

# Step 1: Install local prerequisites
echo -e "\n${BLUE}[1/10] Installing local prerequisites${NC}"

# Check if we have required tools
MISSING_TOOLS=()

if ! command -v zip >/dev/null 2>&1; then
  MISSING_TOOLS+=("zip")
fi

if ! command -v ssh >/dev/null 2>&1; then
  MISSING_TOOLS+=("openssh-client")
fi

if ! command -v rsync >/dev/null 2>&1; then
  MISSING_TOOLS+=("rsync")
fi

if ! command -v curl >/dev/null 2>&1; then
  MISSING_TOOLS+=("curl")
fi

if [ ${#MISSING_TOOLS[@]} -gt 0 ]; then
  echo -e "${YELLOW}[i] Installing missing tools: ${MISSING_TOOLS[*]}${NC}"
  
  if command -v apt-get >/dev/null 2>&1; then
    sudo apt-get update
    sudo apt-get install -y "${MISSING_TOOLS[@]}"
  elif command -v yum >/dev/null 2>&1; then
    sudo yum install -y "${MISSING_TOOLS[@]}"
  elif command -v dnf >/dev/null 2>&1; then
    sudo dnf install -y "${MISSING_TOOLS[@]}"
  else
    echo -e "${RED}[!] Cannot install prerequisites automatically${NC}"
    echo -e "${YELLOW}[i] Please install: ${MISSING_TOOLS[*]}${NC}"
    exit 1
  fi
else
  echo -e "${GREEN}[✓] All prerequisites are available${NC}"
fi

# Step 2: Validate local project
echo -e "\n${BLUE}[2/10] Validating local project${NC}"

if [ ! -f "${LOCAL_PROJECT_DIR}/package.json" ]; then
  echo -e "${RED}[!] package.json not found in ${LOCAL_PROJECT_DIR}${NC}"
  exit 1
fi

if [ ! -f "${LOCAL_PROJECT_DIR}/server.cjs" ]; then
  echo -e "${RED}[!] server.cjs not found in ${LOCAL_PROJECT_DIR}${NC}"
  exit 1
fi

if [ ! -d "${LOCAL_PROJECT_DIR}/src" ]; then
  echo -e "${RED}[!] src directory not found in ${LOCAL_PROJECT_DIR}${NC}"
  exit 1
fi

# Get version from package.json
APP_VERSION=$(grep '"version"' "${LOCAL_PROJECT_DIR}/package.json" | cut -d'"' -f4)
echo -e "${GREEN}[✓] Project validated - Version: ${APP_VERSION}${NC}"

# Step 3: Setup SSH keys if needed
echo -e "\n${BLUE}[3/10] Setting up SSH authentication${NC}"

# Test SSH connection first
if ssh -o ConnectTimeout=10 -o BatchMode=yes "${REMOTE_TARGET}" "echo 'SSH key auth works'" 2>/dev/null; then
  echo -e "${GREEN}[✓] SSH key authentication is working${NC}"
  SSH_METHOD="key"
else
  echo -e "${YELLOW}[!] SSH key authentication not available${NC}"
  echo -e "${YELLOW}[i] Setting up SSH key authentication...${NC}"
  
  # Check if SSH key exists
  if [ ! -f ~/.ssh/id_rsa.pub ] && [ ! -f ~/.ssh/id_ed25519.pub ]; then
    echo -e "${YELLOW}[i] No SSH key found. Generating new SSH key...${NC}"
    read -p "Enter your email for SSH key: " user_email
    ssh-keygen -t ed25519 -C "$user_email" -f ~/.ssh/id_ed25519 -N ""
    echo -e "${GREEN}[✓] SSH key generated${NC}"
  fi
  
  # Copy SSH key to remote server
  echo -e "${YELLOW}[i] Copying SSH key to remote server...${NC}"
  echo -e "${YELLOW}[i] You will be prompted for the password for ${REMOTE_TARGET}${NC}"
  
  if [ -f ~/.ssh/id_ed25519.pub ]; then
    ssh-copy-id -i ~/.ssh/id_ed25519.pub "${REMOTE_TARGET}"
  elif [ -f ~/.ssh/id_rsa.pub ]; then
    ssh-copy-id -i ~/.ssh/id_rsa.pub "${REMOTE_TARGET}"
  else
    echo -e "${RED}[!] No SSH public key found${NC}"
    exit 1
  fi
  
  # Test the connection again
  if ssh -o ConnectTimeout=10 -o BatchMode=yes "${REMOTE_TARGET}" "echo 'SSH key setup successful'" 2>/dev/null; then
    echo -e "${GREEN}[✓] SSH key authentication is now working${NC}"
    SSH_METHOD="key"
  else
    echo -e "${RED}[!] SSH key setup failed${NC}"
    exit 1
  fi
fi

# Step 4: Build application locally
echo -e "\n${BLUE}[4/10] Building application locally${NC}"

cd "${LOCAL_PROJECT_DIR}"

echo -e "${YELLOW}[i] Installing dependencies...${NC}"
npm install

echo -e "${YELLOW}[i] Building for production...${NC}"
npm run build

echo -e "${GREEN}[✓] Application built successfully${NC}"

# Step 5: Create deployment package
echo -e "\n${BLUE}[5/10] Creating deployment package${NC}"

DEPLOY_PACKAGE="/tmp/focusbudget-deploy-$(date +%Y%m%d-%H%M%S).zip"

echo -e "${YELLOW}[i] Creating deployment package: ${DEPLOY_PACKAGE}${NC}"

# Create a temporary directory for packaging
TEMP_PACKAGE_DIR="/tmp/focusbudget-package-$$"
mkdir -p "$TEMP_PACKAGE_DIR"

# Copy necessary files
cp -r dist/ "$TEMP_PACKAGE_DIR/"
cp server.cjs "$TEMP_PACKAGE_DIR/"
cp package.json "$TEMP_PACKAGE_DIR/"
cp package-lock.json "$TEMP_PACKAGE_DIR/" 2>/dev/null || true
cp -r src/ "$TEMP_PACKAGE_DIR/"

# Create the zip file
cd "$TEMP_PACKAGE_DIR"
zip -r "$DEPLOY_PACKAGE" .
cd - > /dev/null

# Cleanup temp directory
rm -rf "$TEMP_PACKAGE_DIR"

echo -e "${GREEN}[✓] Deployment package created: ${DEPLOY_PACKAGE}${NC}"

# Step 6: Setup remote staging and deployment directories
echo -e "\n${BLUE}[6/10] Setting up remote directories${NC}"

REMOTE_STAGING="/tmp/focusbudget-staging-$(date +%Y%m%d-%H%M%S)"
REMOTE_BACKUP="/tmp/focusbudget-backup-$(date +%Y%m%d-%H%M%S)"

ssh "${REMOTE_TARGET}" "
  echo 'Creating staging directory: ${REMOTE_STAGING}'
  mkdir -p '${REMOTE_STAGING}'
  
  # Create backup of existing deployment if it exists
  if [ -d '${APP_ROOT}' ]; then
    echo 'Creating backup of existing deployment...'
    sudo mkdir -p '${REMOTE_BACKUP}'
    sudo cp -r '${APP_ROOT}' '${REMOTE_BACKUP}/app' 2>/dev/null || true
    sudo cp -r '${DATA_ROOT}' '${REMOTE_BACKUP}/data' 2>/dev/null || true
    echo 'Backup created at: ${REMOTE_BACKUP}'
  fi
"

echo -e "${GREEN}[✓] Remote directories prepared${NC}"

# Step 7: Transfer and extract deployment package
echo -e "\n${BLUE}[7/10] Transferring deployment package${NC}"

echo -e "${YELLOW}[i] Uploading deployment package...${NC}"
scp "$DEPLOY_PACKAGE" "${REMOTE_TARGET}:${REMOTE_STAGING}/package.zip"

echo -e "${YELLOW}[i] Extracting package on remote server...${NC}"
ssh "${REMOTE_TARGET}" "
  cd '${REMOTE_STAGING}'
  unzip -q package.zip
  rm package.zip
  echo 'Package extracted successfully'
  ls -la
"

# Cleanup local package
rm -f "$DEPLOY_PACKAGE"

echo -e "${GREEN}[✓] Deployment package transferred and extracted${NC}"

# Step 8: Setup service account and install application
echo -e "\n${BLUE}[8/10] Setting up service account and installing application${NC}"

# Create a setup script on the remote server that handles all sudo operations
ssh "${REMOTE_TARGET}" "cat > '${REMOTE_STAGING}/setup-remote.sh' << 'SETUP_EOF'
#!/bin/bash
set -e

APP_NAME='${APP_NAME}'
APP_USER='${APP_USER}'
APP_GROUP='${APP_GROUP}'
APP_ROOT='${APP_ROOT}'
DATA_ROOT='${DATA_ROOT}'
LOG_ROOT='${LOG_ROOT}'
REMOTE_STAGING='${REMOTE_STAGING}'

echo 'Starting remote setup...'

# Install prerequisites
if ! command -v curl >/dev/null 2>&1; then
  echo 'Installing curl...'
  apt-get update
  apt-get install -y curl
fi

# Install Node.js if not present
if ! command -v node >/dev/null 2>&1; then
  echo 'Installing Node.js...'
  curl -fsSL https://deb.nodesource.com/setup_20.x | bash -
  apt-get install -y nodejs
fi

# Create group if it doesn't exist
if ! getent group \"\$APP_GROUP\" >/dev/null 2>&1; then
  echo \"Creating group: \$APP_GROUP\"
  groupadd --system \"\$APP_GROUP\"
fi

# Create user if it doesn't exist
if ! getent passwd \"\$APP_USER\" >/dev/null 2>&1; then
  echo \"Creating user: \$APP_USER\"
  useradd --system --gid \"\$APP_GROUP\" --home-dir \"\$APP_ROOT\" \
    --shell /bin/false --comment 'FocusBudget Application User' \"\$APP_USER\"
fi

# Create application directories
mkdir -p \"\$APP_ROOT\"
mkdir -p \"\$DATA_ROOT\"
mkdir -p \"\$DATA_ROOT/history\"
mkdir -p \"\$LOG_ROOT\"

# Stop existing service if running
systemctl stop \"\$APP_NAME\" 2>/dev/null || true

# Install application files
echo 'Installing application files...'
cp -r \"\$REMOTE_STAGING\"/* \"\$APP_ROOT\"/

# Install Node.js dependencies
cd \"\$APP_ROOT\"
npm install --production

# Set proper ownership and permissions
chown -R \"\$APP_USER:\$APP_GROUP\" \"\$APP_ROOT\"
chown -R \"\$APP_USER:\$APP_GROUP\" \"\$DATA_ROOT\"
chown -R \"\$APP_USER:\$APP_GROUP\" \"\$LOG_ROOT\"

chmod 755 \"\$APP_ROOT\"
chmod 644 \"\$APP_ROOT\"/*.json 2>/dev/null || true
chmod 644 \"\$APP_ROOT\"/*.cjs 2>/dev/null || true
chmod -R 755 \"\$APP_ROOT/dist\" 2>/dev/null || true
chmod -R 755 \"\$APP_ROOT/node_modules\" 2>/dev/null || true
chmod 750 \"\$DATA_ROOT\"
chmod 750 \"\$LOG_ROOT\"

# Create environment file
cat > \"\$APP_ROOT/.env\" << ENV_EOF
NODE_ENV=production
PORT=3001
SERVER_PORT=3001
DATA_DIR=\$DATA_ROOT
LOG_DIR=\$LOG_ROOT
ENV_EOF

chown \"\$APP_USER:\$APP_GROUP\" \"\$APP_ROOT/.env\"
chmod 640 \"\$APP_ROOT/.env\"

echo 'Remote setup completed successfully!'
SETUP_EOF

chmod +x '${REMOTE_STAGING}/setup-remote.sh'
"

echo -e "${YELLOW}[i] Running setup script with sudo...${NC}"
echo -e "${YELLOW}[i] You may be prompted for the sudo password${NC}"

# Run the setup script with sudo
ssh -t "${REMOTE_TARGET}" "sudo '${REMOTE_STAGING}/setup-remote.sh'"

echo -e "${GREEN}[✓] Service account and application installed${NC}"

# Step 9: Setup systemd service and Nginx
echo -e "\n${BLUE}[9/10] Setting up systemd service and Nginx${NC}"

# Create service and nginx setup script
ssh "${REMOTE_TARGET}" "cat > '${REMOTE_STAGING}/setup-services.sh' << 'SERVICES_EOF'
#!/bin/bash
set -e

APP_NAME='${APP_NAME}'
APP_USER='${APP_USER}'
APP_GROUP='${APP_GROUP}'
APP_ROOT='${APP_ROOT}'
DATA_ROOT='${DATA_ROOT}'
LOG_ROOT='${LOG_ROOT}'

echo 'Setting up systemd service...'

# Create systemd service
tee /etc/systemd/system/\$APP_NAME.service > /dev/null << SERVICE_EOF
[Unit]
Description=FocusBudget Personal Budget Management Application
After=network.target
Wants=network-online.target

[Service]
Type=simple
User=\$APP_USER
Group=\$APP_GROUP
WorkingDirectory=\$APP_ROOT
ExecStart=/usr/bin/node server.cjs
Restart=on-failure
RestartSec=10
TimeoutStartSec=30
TimeoutStopSec=30

Environment=NODE_ENV=production
Environment=PORT=3001
Environment=DATA_DIR=\$DATA_ROOT
Environment=LOG_DIR=\$LOG_ROOT
EnvironmentFile=\$APP_ROOT/.env

# Security settings
NoNewPrivileges=true
PrivateTmp=true
PrivateDevices=true
ProtectHome=true
ProtectSystem=strict
ReadWritePaths=\$DATA_ROOT
ReadWritePaths=\$LOG_ROOT
ReadOnlyPaths=\$APP_ROOT

[Install]
WantedBy=multi-user.target
SERVICE_EOF

echo 'Installing and configuring Nginx...'

# Install Nginx
apt-get update
apt-get install -y nginx

# Create Nginx configuration
tee /etc/nginx/sites-available/\$APP_NAME > /dev/null << NGINX_EOF
server {
    listen 80;
    listen [::]:80;
    server_name _;

    # Security headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection \"1; mode=block\" always;
    add_header Referrer-Policy strict-origin-when-cross-origin always;
    server_tokens off;

    # Increase client max body size for CSV uploads
    client_max_body_size 10M;

    # Main application proxy
    location / {
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \\\$http_upgrade;
        proxy_set_header Connection \"upgrade\";
        proxy_set_header Host \\\$host;
        proxy_set_header X-Real-IP \\\$remote_addr;
        proxy_set_header X-Forwarded-For \\\$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \\\$scheme;
        proxy_cache_bypass \\\$http_upgrade;

        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # API endpoints
    location /api/ {
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Host \\\$host;
        proxy_set_header X-Real-IP \\\$remote_addr;
        proxy_set_header X-Forwarded-For \\\$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \\\$scheme;

        proxy_connect_timeout 120s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;
    }

    # Health check endpoint
    location /health {
        proxy_pass http://127.0.0.1:3001/api/status;
        proxy_set_header Host \\\$host;
        access_log off;
    }
}
NGINX_EOF

# Enable the site and disable default
mkdir -p /etc/nginx/sites-enabled
ln -sf /etc/nginx/sites-available/\$APP_NAME /etc/nginx/sites-enabled/\$APP_NAME
rm -f /etc/nginx/sites-enabled/default

# Test Nginx configuration
if nginx -t; then
  echo 'Nginx configuration is valid'
else
  echo 'Nginx configuration test failed'
  exit 1
fi

# Enable and start services
systemctl daemon-reload
systemctl enable \$APP_NAME
systemctl enable nginx
systemctl restart nginx
systemctl start \$APP_NAME

echo 'Services setup completed successfully!'
SERVICES_EOF

chmod +x '${REMOTE_STAGING}/setup-services.sh'
"

echo -e "${YELLOW}[i] Running services setup script with sudo...${NC}"
echo -e "${YELLOW}[i] You may be prompted for the sudo password${NC}"

# Run the services setup script with sudo
ssh -t "${REMOTE_TARGET}" "sudo '${REMOTE_STAGING}/setup-services.sh'"

echo -e "${GREEN}[✓] Services configured and started${NC}"

# Step 10: Test and validate deployment
echo -e "\n${BLUE}[10/10] Testing and validating deployment${NC}"

# Wait for services to start
echo -e "${YELLOW}[i] Waiting for services to start...${NC}"
sleep 5

# Test deployment
ssh "${REMOTE_TARGET}" "
  echo 'Testing service status...'

  # Check FocusBudget service
  if systemctl is-active --quiet ${APP_NAME} 2>/dev/null; then
    echo '✓ FocusBudget service is running'
  else
    echo '✗ FocusBudget service is not running'
    systemctl status ${APP_NAME} --no-pager 2>/dev/null || echo 'Cannot check service status'
  fi

  # Check Nginx service
  if systemctl is-active --quiet nginx 2>/dev/null; then
    echo '✓ Nginx service is running'
  else
    echo '✗ Nginx service is not running'
    systemctl status nginx --no-pager 2>/dev/null || echo 'Cannot check nginx status'
  fi

  echo ''
  echo 'Testing connectivity...'

  # Test port 80 (Nginx)
  if curl -s -f http://localhost/ > /dev/null; then
    echo '✓ Port 80 (Nginx) is responding'
  else
    echo '✗ Port 80 (Nginx) is not responding'
  fi

  # Test port 3001 (direct backend)
  if curl -s -f http://localhost:3001/ > /dev/null; then
    echo '✓ Port 3001 (Backend) is responding'
  else
    echo '✗ Port 3001 (Backend) is not responding'
  fi

  # Test health endpoint
  if curl -s -f http://localhost/health > /dev/null; then
    echo '✓ Health endpoint is responding'
  else
    echo '✗ Health endpoint is not responding'
  fi

  echo ''
  echo 'Testing version endpoint...'

  # Test version endpoint and display version
  VERSION_RESPONSE=\$(curl -s http://localhost/api/version 2>/dev/null || curl -s http://localhost:3001/api/version 2>/dev/null)
  if [ -n \"\$VERSION_RESPONSE\" ]; then
    echo '✓ Version endpoint is responding'
    echo \"Version info: \$VERSION_RESPONSE\"
  else
    echo '✗ Version endpoint is not responding'
  fi

  echo ''
  echo 'Port status:'
  netstat -tln 2>/dev/null | grep -E ':(80|3001) ' || ss -tln 2>/dev/null | grep -E ':(80|3001) ' || echo 'No services found on ports 80 or 3001'
"

# Cleanup staging directory
echo -e "\n${YELLOW}[i] Cleaning up staging directory...${NC}"
ssh "${REMOTE_TARGET}" "rm -rf '${REMOTE_STAGING}'"

echo -e "\n${GREEN}==============================="
echo -e " Deployment Complete!"
echo -e "===============================${NC}"

echo -e "\n${BLUE}Deployment Summary:${NC}"
echo -e "${GREEN}[✓] Application version ${APP_VERSION} deployed successfully${NC}"
echo -e "${GREEN}[✓] Service user '${APP_USER}' created with restricted permissions${NC}"
echo -e "${GREEN}[✓] Application installed to ${APP_ROOT}${NC}"
echo -e "${GREEN}[✓] Data directory: ${DATA_ROOT}${NC}"
echo -e "${GREEN}[✓] Log directory: ${LOG_ROOT}${NC}"
echo -e "${GREEN}[✓] Systemd service '${APP_NAME}' configured and running${NC}"
echo -e "${GREEN}[✓] Nginx reverse proxy configured on port 80${NC}"

echo -e "\n${BLUE}Access Points:${NC}"
echo -e "${YELLOW}• Primary Application: http://${REMOTE_HOST}${NC}"
echo -e "${YELLOW}• Health Check: http://${REMOTE_HOST}/health${NC}"
echo -e "${YELLOW}• Version Info: http://${REMOTE_HOST}/api/version${NC}"
echo -e "${YELLOW}• API Status: http://${REMOTE_HOST}/api/status${NC}"

echo -e "\n${BLUE}Management Commands:${NC}"
echo -e "${YELLOW}• Service status: ssh ${REMOTE_TARGET} 'sudo systemctl status ${APP_NAME}'${NC}"
echo -e "${YELLOW}• View logs: ssh ${REMOTE_TARGET} 'sudo journalctl -u ${APP_NAME} -f'${NC}"
echo -e "${YELLOW}• Restart service: ssh ${REMOTE_TARGET} 'sudo systemctl restart ${APP_NAME}'${NC}"
echo -e "${YELLOW}• Nginx status: ssh ${REMOTE_TARGET} 'sudo systemctl status nginx'${NC}"
echo -e "${YELLOW}• Check version: curl -s http://${REMOTE_HOST}/api/version${NC}"

echo -e "\n${BLUE}Security Features:${NC}"
echo -e "${YELLOW}• Dedicated system user with no shell access${NC}"
echo -e "${YELLOW}• Restricted file permissions and ownership${NC}"
echo -e "${YELLOW}• Systemd security sandbox enabled${NC}"
echo -e "${YELLOW}• Nginx security headers configured${NC}"

echo -e "\n${BLUE}Backup Information:${NC}"
if ssh "${REMOTE_TARGET}" "[ -d '${REMOTE_BACKUP}' ]" 2>/dev/null; then
  echo -e "${YELLOW}• Previous deployment backed up to: ${REMOTE_BACKUP}${NC}"
else
  echo -e "${YELLOW}• No previous deployment found (fresh installation)${NC}"
fi

echo -e "\n${GREEN}FocusBudget v${APP_VERSION} is now running securely on ${REMOTE_HOST}!${NC}"
echo -e "${YELLOW}You can access the application at: http://${REMOTE_HOST}${NC}"

# Final version validation
echo -e "\n${BLUE}Final Version Validation:${NC}"
DEPLOYED_VERSION=$(curl -s "http://${REMOTE_HOST}/api/version" 2>/dev/null | grep -o '"version":"[^"]*"' | cut -d'"' -f4 2>/dev/null || echo "unknown")
if [ "$DEPLOYED_VERSION" = "$APP_VERSION" ]; then
  echo -e "${GREEN}[✓] Deployed version matches expected version: ${APP_VERSION}${NC}"
else
  echo -e "${YELLOW}[!] Version mismatch - Expected: ${APP_VERSION}, Deployed: ${DEPLOYED_VERSION}${NC}"
fi
