# FocusBudget Deployment

## Quick Start

To deploy FocusBudget to your remote Ubuntu 24.04 LXC container:

```bash
./deploy-complete.sh
```

## What This Does

The script performs a complete 10-step deployment:

1. **Install Prerequisites** - zip, ssh, rsync, curl
2. **Validate Project** - checks structure, gets version (1.0.0)
3. **Setup SSH Keys** - handles password prompts for initial setup
4. **Build Application** - npm install & build locally
5. **Create Package** - zip deployment package
6. **Setup Remote Dirs** - staging and backup directories
7. **Transfer Package** - upload and extract
8. **Setup Service Account** - dedicated user, permissions, systemd
9. **Configure Services** - Nginx reverse proxy on port 80 → 3001
10. **Test & Validate** - comprehensive testing and version validation

## Key Features

- **Version Display**: Shows v1.0.0 in application header
- **Secure Setup**: Dedicated `focusbudget_user` with restricted permissions
- **Nginx Reverse Proxy**: Port 80 → 3001 with security headers
- **Systemd Service**: Auto-restart, security hardening
- **Version Validation**: Confirms deployed version matches expected

## After Deployment

- **Access**: http://**********
- **Version Check**: `curl -s http://**********/api/version`
- **Health Check**: http://**********/health

## Files

- `deploy-complete.sh` - Main deployment script
- `project/COMPLETE_DEPLOYMENT_GUIDE.md` - Detailed documentation
- `server.cjs` - Backend server with fixed route order
- `src/App.tsx` - Frontend with version display
- `package.json` - Version 1.0.0

## Troubleshooting

If deployment fails:
1. Check SSH connectivity: `ssh labmaster@**********`
2. Ensure sudo access on remote server
3. Check the error message and re-run the script

The script is safe to re-run and will provide clear feedback at each step.

## Clean Directory

All old deployment scripts have been removed. Only essential files remain:
- Main deployment script
- Application source code
- Documentation
- Built application (dist/)
