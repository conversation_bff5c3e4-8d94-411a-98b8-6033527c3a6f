#!/bin/bash

# FocusBudget Secure Build and Deploy Script
# This script builds the app for production and sets up a secure full-stack server
# with dedicated user, proper permissions, and organized directory structure

# Exit on error
set -e

# Colors for better readability
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

echo -e "\n${BLUE}==============================="
echo -e " FocusBudget Secure Production Setup"
echo -e "===============================${NC}\n"

# Configuration
APP_NAME="focusbudget"
APP_USER="${APP_NAME}_user"
APP_GROUP="${APP_NAME}_group"
APP_ROOT="/opt/${APP_NAME}"
DATA_ROOT="/var/lib/${APP_NAME}"
LOG_ROOT="/var/log/${APP_NAME}"
SERVICE_NAME="${APP_NAME}"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
  echo -e "${RED}[!] package.json not found. Please run this script from the project directory.${NC}"
  echo -e "${YELLOW}[i] Current directory: $(pwd)${NC}"
  echo -e "${YELLOW}[i] Expected files: package.json, server.cjs, src/App.tsx${NC}"
  exit 1
fi

# Check if server.cjs exists
if [ ! -f "server.cjs" ]; then
  echo -e "${RED}[!] server.cjs not found. Please ensure the backend server file exists.${NC}"
  echo -e "${YELLOW}[i] Looking for server.cjs in: $(pwd)${NC}"
  exit 1
fi

# Check if src directory exists (React app structure)
if [ ! -d "src" ]; then
  echo -e "${RED}[!] src directory not found. This doesn't appear to be a React project.${NC}"
  echo -e "${YELLOW}[i] Current directory: $(pwd)${NC}"
  exit 1
fi

echo -e "${GREEN}[✓] Project structure validated${NC}"

# Check if running as root or with sudo access
if [ "$EUID" -eq 0 ]; then
  echo -e "${YELLOW}[i] Running as root - will create dedicated user${NC}"
  SUDO_CMD=""
elif command -v sudo >/dev/null 2>&1; then
  echo -e "${YELLOW}[i] Will use sudo for system operations${NC}"
  SUDO_CMD="sudo"
else
  echo -e "${RED}[!] This script requires root access or sudo. Please run with sudo.${NC}"
  exit 1
fi

# Function to run commands with appropriate privileges
run_privileged() {
  if [ -n "$SUDO_PASSWORD" ] && [ -n "$SUDO_CMD" ]; then
    echo "$SUDO_PASSWORD" | $SUDO_CMD -S "$@"
  elif [ -n "$SUDO_CMD" ]; then
    $SUDO_CMD "$@"
  else
    "$@"
  fi
}

# Check if Node.js is installed, install if missing
if ! command -v node >/dev/null 2>&1; then
  echo -e "${YELLOW}[i] Node.js not found. Installing Node.js 20 LTS...${NC}"

  # Detect the operating system and install Node.js accordingly
  if command -v apt-get >/dev/null 2>&1; then
    echo -e "${BLUE}[>] Installing Node.js using apt-get (Ubuntu/Debian)${NC}"
    run_privileged apt-get update
    
    if ! command -v curl >/dev/null 2>&1; then
      echo -e "${BLUE}[>] Installing curl...${NC}"
      run_privileged apt-get install -y curl
    fi

    # Add NodeSource repository and install Node.js 20
    echo -e "${BLUE}[>] Adding NodeSource repository...${NC}"
    curl -fsSL https://deb.nodesource.com/setup_20.x | run_privileged bash -
    
    echo -e "${BLUE}[>] Installing Node.js...${NC}"
    run_privileged apt-get install -y nodejs

  elif command -v yum >/dev/null 2>&1; then
    echo -e "${BLUE}[>] Installing Node.js using yum (RHEL/CentOS)${NC}"
    curl -fsSL https://rpm.nodesource.com/setup_20.x | run_privileged bash -
    run_privileged yum install -y nodejs

  elif command -v dnf >/dev/null 2>&1; then
    echo -e "${BLUE}[>] Installing Node.js using dnf (Fedora)${NC}"
    curl -fsSL https://rpm.nodesource.com/setup_20.x | run_privileged bash -
    run_privileged dnf install -y nodejs

  else
    echo -e "${RED}[!] Could not detect package manager. Please install Node.js manually.${NC}"
    exit 1
  fi

  # Verify installation
  if command -v node >/dev/null 2>&1; then
    echo -e "${GREEN}[✓] Node.js $(node --version) installed successfully${NC}"
  else
    echo -e "${RED}[!] Node.js installation failed${NC}"
    exit 1
  fi
fi

# Check Node.js version
NODE_VERSION=$(node --version 2>/dev/null | cut -d'v' -f2 | cut -d'.' -f1)
if [ -z "$NODE_VERSION" ] || [ "$NODE_VERSION" -lt 18 ]; then
  echo -e "${RED}[!] Node.js 18+ is required. Current version: $(node --version 2>/dev/null || echo 'unknown')${NC}"
  exit 1
fi

echo -e "${GREEN}[✓] Node.js $(node --version) and npm $(npm --version) are available${NC}"

# 1. Install dependencies and build
echo -e "\n${BLUE}[>] Installing dependencies${NC}"
npm install
echo -e "${GREEN}[✓] Dependencies installed${NC}"

echo -e "\n${BLUE}[>] Building application for production${NC}"
npm run build
echo -e "${GREEN}[✓] Build completed${NC}"

# 2. Create dedicated user and group
echo -e "\n${BLUE}[>] Setting up dedicated user and group${NC}"

# Create group if it doesn't exist
if ! getent group "$APP_GROUP" >/dev/null 2>&1; then
  echo -e "${YELLOW}[i] Creating group: $APP_GROUP${NC}"
  run_privileged groupadd --system "$APP_GROUP"
else
  echo -e "${YELLOW}[i] Group $APP_GROUP already exists${NC}"
fi

# Create user if it doesn't exist
if ! getent passwd "$APP_USER" >/dev/null 2>&1; then
  echo -e "${YELLOW}[i] Creating user: $APP_USER${NC}"
  run_privileged useradd --system --gid "$APP_GROUP" --home-dir "$APP_ROOT" \
    --shell /bin/false --comment "FocusBudget Application User" "$APP_USER"
else
  echo -e "${YELLOW}[i] User $APP_USER already exists${NC}"
fi

echo -e "${GREEN}[✓] User and group configured${NC}"

# 3. Create directory structure
echo -e "\n${BLUE}[>] Setting up directory structure${NC}"

# Create application directory
if [ ! -d "$APP_ROOT" ]; then
  run_privileged mkdir -p "$APP_ROOT"
fi

# Create data directory
if [ ! -d "$DATA_ROOT" ]; then
  run_privileged mkdir -p "$DATA_ROOT"
  run_privileged mkdir -p "$DATA_ROOT/history"
fi

# Create log directory
if [ ! -d "$LOG_ROOT" ]; then
  run_privileged mkdir -p "$LOG_ROOT"
fi

echo -e "${GREEN}[✓] Directory structure created${NC}"

# 4. Copy application files
echo -e "\n${BLUE}[>] Installing application files${NC}"

# Stop service if running
if run_privileged systemctl is-active --quiet "$SERVICE_NAME" 2>/dev/null; then
  echo -e "${YELLOW}[i] Stopping existing service${NC}"
  run_privileged systemctl stop "$SERVICE_NAME"
fi

# Copy application files
run_privileged cp -r dist/ "$APP_ROOT/"
run_privileged cp server.cjs "$APP_ROOT/"
run_privileged cp package.json "$APP_ROOT/"
run_privileged cp -r node_modules/ "$APP_ROOT/"
run_privileged cp -r src/ "$APP_ROOT/"

echo -e "${GREEN}[✓] Application files installed${NC}"

# 5. Set proper ownership and permissions
echo -e "\n${BLUE}[>] Setting file permissions${NC}"

# Set ownership
run_privileged chown -R "$APP_USER:$APP_GROUP" "$APP_ROOT"
run_privileged chown -R "$APP_USER:$APP_GROUP" "$DATA_ROOT"
run_privileged chown -R "$APP_USER:$APP_GROUP" "$LOG_ROOT"

# Set permissions
run_privileged chmod 755 "$APP_ROOT"
run_privileged chmod 644 "$APP_ROOT"/*.json
run_privileged chmod 644 "$APP_ROOT"/*.cjs
run_privileged chmod -R 755 "$APP_ROOT/dist"
run_privileged chmod -R 755 "$APP_ROOT/node_modules"
run_privileged chmod -R 755 "$APP_ROOT/src"

# Data directory permissions
run_privileged chmod 750 "$DATA_ROOT"
run_privileged chmod 750 "$DATA_ROOT/history"

# Log directory permissions
run_privileged chmod 750 "$LOG_ROOT"

echo -e "${GREEN}[✓] File permissions configured${NC}"

# 6. Create environment configuration
echo -e "\n${BLUE}[>] Setting up environment configuration${NC}"
ENV_FILE="$APP_ROOT/.env"

# Create .env file
run_privileged tee "$ENV_FILE" > /dev/null << EOF
# FocusBudget Production Configuration
NODE_ENV=production
PORT=3001
SERVER_PORT=3001
DATA_DIR=$DATA_ROOT
LOG_DIR=$LOG_ROOT
# OPENAI_API_KEY=your_openai_api_key_here
EOF

run_privileged chown "$APP_USER:$APP_GROUP" "$ENV_FILE"
run_privileged chmod 640 "$ENV_FILE"

echo -e "${GREEN}[✓] Environment file created${NC}"

# 7. Create systemd service file
echo -e "\n${BLUE}[>] Setting up systemd service${NC}"
SERVICE_PATH="/etc/systemd/system/${SERVICE_NAME}.service"

# Get Node.js path
NODE_PATH=$(which node)

# Create service file content with enhanced security
SERVICE_CONTENT="[Unit]
Description=FocusBudget Personal Budget Management Application
After=network.target
Wants=network-online.target

[Service]
Type=simple
User=$APP_USER
Group=$APP_GROUP
WorkingDirectory=$APP_ROOT
ExecStart=$NODE_PATH server.cjs
Restart=on-failure
RestartSec=10
TimeoutStartSec=30
TimeoutStopSec=30

# Environment
Environment=NODE_ENV=production
Environment=PORT=3001
Environment=DATA_DIR=$DATA_ROOT
Environment=LOG_DIR=$LOG_ROOT
EnvironmentFile=$ENV_FILE

# Security settings
NoNewPrivileges=true
PrivateTmp=true
PrivateDevices=true
ProtectHome=true
ProtectSystem=strict
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true
RestrictRealtime=true
RestrictSUIDSGID=true
RemoveIPC=true
PrivateMounts=true

# File system access
ReadWritePaths=$DATA_ROOT
ReadWritePaths=$LOG_ROOT
ReadOnlyPaths=$APP_ROOT

# Network restrictions
RestrictAddressFamilies=AF_INET AF_INET6 AF_UNIX
IPAddressDeny=any
IPAddressAllow=localhost
IPAddressAllow=*********/8
IPAddressAllow=::1/128

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target"

# Create service file
echo -e "${YELLOW}[i] Creating systemd service file${NC}"
echo "$SERVICE_CONTENT" | run_privileged tee "$SERVICE_PATH" > /dev/null

echo -e "${GREEN}[✓] Service file created at $SERVICE_PATH${NC}"

# 8. Enable and start the service
echo -e "\n${BLUE}[>] Enabling and starting service${NC}"
run_privileged systemctl daemon-reload
run_privileged systemctl enable "$SERVICE_NAME"

# Stop any existing services (including old budgetpage service)
for old_service in budgetpage focusbudget; do
  if run_privileged systemctl is-active --quiet "$old_service" 2>/dev/null; then
    echo -e "${YELLOW}[i] Stopping existing $old_service service${NC}"
    run_privileged systemctl stop "$old_service"
  fi
done

# Start the new service
echo -e "${BLUE}[>] Starting $SERVICE_NAME service${NC}"
run_privileged systemctl start "$SERVICE_NAME"

# Wait a moment for service to start
sleep 3

# Check status
echo -e "\n${BLUE}[>] Service status:${NC}"
run_privileged systemctl status "$SERVICE_NAME" --no-pager

echo -e "\n${GREEN}[✓] FocusBudget is now running as a secure service!${NC}"

# 9. Set up Nginx reverse proxy
echo -e "\n${BLUE}[>] Setting up Nginx reverse proxy${NC}"

# Check if Nginx is installed, if not, install it
if ! command -v nginx >/dev/null 2>&1; then
  echo -e "${YELLOW}[i] Nginx not found, installing...${NC}"

  if command -v apt-get >/dev/null 2>&1; then
    run_privileged apt-get update
    run_privileged apt-get install -y nginx
  elif command -v yum >/dev/null 2>&1; then
    run_privileged yum install -y nginx
  elif command -v dnf >/dev/null 2>&1; then
    run_privileged dnf install -y nginx
  else
    echo -e "${RED}[!] Could not install Nginx automatically${NC}"
    exit 1
  fi

  echo -e "${GREEN}[✓] Nginx installed${NC}"
else
  echo -e "${GREEN}[✓] Nginx is already installed${NC}"
fi

# Create Nginx configuration
NGINX_CONF="/etc/nginx/sites-available/${SERVICE_NAME}"
NGINX_ENABLED="/etc/nginx/sites-enabled/${SERVICE_NAME}"

# Ensure directories exist
run_privileged mkdir -p /etc/nginx/sites-available
run_privileged mkdir -p /etc/nginx/sites-enabled

# Enhanced Nginx configuration with security headers
NGINX_CONTENT="# FocusBudget Nginx Configuration
# Generated by build-and-deploy-secure.sh

server {
    listen 80;
    listen [::]:80;
    server_name _;  # Change to your domain if applicable

    # Security headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection \"1; mode=block\" always;
    add_header Referrer-Policy strict-origin-when-cross-origin always;
    add_header Content-Security-Policy \"default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self';\" always;

    # Hide Nginx version
    server_tokens off;

    # Increase client max body size for CSV uploads
    client_max_body_size 10M;

    # Rate limiting
    limit_req_zone \$binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone \$binary_remote_addr zone=general:10m rate=30r/s;

    # Main application proxy
    location / {
        limit_req zone=general burst=20 nodelay;

        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;

        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # API specific settings with rate limiting
    location /api/ {
        limit_req zone=api burst=5 nodelay;

        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;

        # Extended timeout for processing
        proxy_connect_timeout 120s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;
    }

    # Health check endpoint (no rate limiting)
    location /health {
        proxy_pass http://127.0.0.1:3001/api/status;
        proxy_set_header Host \$host;
        access_log off;
    }
}"

echo -e "${BLUE}[>] Creating Nginx configuration${NC}"
echo "$NGINX_CONTENT" | run_privileged tee "$NGINX_CONF" > /dev/null

# Remove any existing configuration and install new one
if [ -f "$NGINX_ENABLED" ]; then
  echo -e "${YELLOW}[i] Removing existing Nginx configuration${NC}"
  run_privileged rm -f "$NGINX_ENABLED"
fi

run_privileged ln -sf "$NGINX_CONF" "$NGINX_ENABLED"

# Test Nginx configuration
echo -e "${BLUE}[>] Testing Nginx configuration${NC}"
if run_privileged nginx -t; then
  echo -e "${GREEN}[✓] Nginx configuration is valid${NC}"

  # Enable and start Nginx
  echo -e "${BLUE}[>] Starting Nginx service${NC}"
  run_privileged systemctl enable nginx
  run_privileged systemctl restart nginx

  if run_privileged systemctl is-active --quiet nginx; then
    echo -e "${GREEN}[✓] Nginx is running and configured!${NC}"
  else
    echo -e "${RED}[!] Nginx failed to start${NC}"
  fi
else
  echo -e "${RED}[!] Nginx configuration test failed${NC}"
fi

# 10. Final setup summary
echo -e "\n${GREEN}==============================="
echo -e " Secure Production Setup Complete!"
echo -e "===============================${NC}"
echo -e "${GREEN}[✓] Application built and deployed securely${NC}"
echo -e "${GREEN}[✓] Dedicated user created: $APP_USER${NC}"
echo -e "${GREEN}[✓] Application directory: $APP_ROOT${NC}"
echo -e "${GREEN}[✓] Data directory: $DATA_ROOT${NC}"
echo -e "${GREEN}[✓] Log directory: $LOG_ROOT${NC}"
echo -e "${GREEN}[✓] Systemd service installed with security hardening${NC}"
echo -e "${GREEN}[✓] Nginx reverse proxy configured with rate limiting${NC}"

echo -e "\n${BLUE}Access Points:${NC}"
echo -e "${YELLOW}• Primary: http://localhost (port 80 via Nginx)${NC}"
echo -e "${YELLOW}• Primary: http://your_server_ip (port 80 via Nginx)${NC}"
echo -e "${YELLOW}• Health: http://localhost/health${NC}"

echo -e "\n${BLUE}Security Features:${NC}"
echo -e "${YELLOW}• Dedicated user: $APP_USER (no shell access)${NC}"
echo -e "${YELLOW}• Restricted file permissions${NC}"
echo -e "${YELLOW}• Systemd security hardening enabled${NC}"
echo -e "${YELLOW}• Network access restricted to localhost${NC}"
echo -e "${YELLOW}• Rate limiting on API endpoints${NC}"
echo -e "${YELLOW}• Security headers configured${NC}"

echo -e "\n${BLUE}Next Steps:${NC}"
echo -e "${YELLOW}1. Edit $ENV_FILE to add your OpenAI API key (optional)${NC}"
echo -e "${YELLOW}   sudo nano $ENV_FILE${NC}"
echo -e "${YELLOW}2. Restart service after config changes:${NC}"
echo -e "${YELLOW}   sudo systemctl restart $SERVICE_NAME${NC}"
echo -e "${YELLOW}3. Access the application at http://localhost${NC}"

echo -e "\n${BLUE}Service Management:${NC}"
echo -e "${YELLOW}• FocusBudget: sudo systemctl [start|stop|restart|status] $SERVICE_NAME${NC}"
echo -e "${YELLOW}• Nginx: sudo systemctl [start|stop|restart|status] nginx${NC}"
echo -e "${YELLOW}• FocusBudget logs: sudo journalctl -u $SERVICE_NAME -f${NC}"
echo -e "${YELLOW}• Nginx logs: sudo journalctl -u nginx -f${NC}"

echo -e "\n${BLUE}File Locations:${NC}"
echo -e "${YELLOW}• Application: $APP_ROOT${NC}"
echo -e "${YELLOW}• Data: $DATA_ROOT${NC}"
echo -e "${YELLOW}• Logs: $LOG_ROOT${NC}"
echo -e "${YELLOW}• Service: /etc/systemd/system/$SERVICE_NAME.service${NC}"
echo -e "${YELLOW}• Nginx config: /etc/nginx/sites-available/$SERVICE_NAME${NC}"
echo -e "${YELLOW}• Environment: $ENV_FILE${NC}"

echo -e "\n${BLUE}Troubleshooting:${NC}"
echo -e "${YELLOW}• Check service status: sudo systemctl status $SERVICE_NAME${NC}"
echo -e "${YELLOW}• View recent logs: sudo journalctl -u $SERVICE_NAME --since '10 minutes ago'${NC}"
echo -e "${YELLOW}• Test Nginx config: sudo nginx -t${NC}"
echo -e "${YELLOW}• Check file permissions: ls -la $APP_ROOT${NC}"

echo -e "\n${GREEN}FocusBudget is ready for secure production use!${NC}"
echo -e "${YELLOW}The application runs as user '$APP_USER' with restricted permissions.${NC}"
echo -e "${YELLOW}All data is stored in $DATA_ROOT with proper ownership.${NC}"
