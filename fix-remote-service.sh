#!/bin/bash

# Quick fix script for remote FocusBudget service issues
# This script fixes the service configuration and deploys the corrected server.cjs

# Colors for better readability
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

# Configuration
REMOTE_USER="labmaster"
REMOTE_HOST="**********"
REMOTE_PATH="/home/<USER>/focusbudget"

echo -e "\n${BLUE}==============================="
echo -e " FocusBudget Service Fix"
echo -e "===============================${NC}\n"

echo -e "${YELLOW}This script will:${NC}"
echo -e "${YELLOW}1. Copy the fixed server.cjs to the remote server${NC}"
echo -e "${YELLOW}2. Fix the systemd service configuration${NC}"
echo -e "${YELLOW}3. Restart the service${NC}\n"

# Check if we can connect via SSH key
if ssh -o ConnectTimeout=10 -o BatchMode=yes "${REMOTE_USER}@${REMOTE_HOST}" exit 2>/dev/null; then
    echo -e "${GREEN}[✓] SSH key authentication working${NC}"
    USE_SSH_KEY=true
else
    echo -e "${YELLOW}[i] SSH key authentication not available, will use password${NC}"
    USE_SSH_KEY=false
    
    # Check if sshpass is installed
    if ! command -v sshpass >/dev/null 2>&1; then
        echo -e "${YELLOW}[i] Installing sshpass...${NC}"
        if command -v apt-get >/dev/null 2>&1; then
            sudo apt-get update && sudo apt-get install -y sshpass
        else
            echo -e "${RED}[!] Please install sshpass manually${NC}"
            exit 1
        fi
    fi
    
    read -s -p "Enter password for ${REMOTE_USER}@${REMOTE_HOST}: " REMOTE_PASSWORD
    echo
fi

# Function to execute remote commands
remote_exec() {
    local command="$1"
    if [ "$USE_SSH_KEY" = true ]; then
        ssh "${REMOTE_USER}@${REMOTE_HOST}" "$command"
    else
        sshpass -p "$REMOTE_PASSWORD" ssh "${REMOTE_USER}@${REMOTE_HOST}" "$command"
    fi
}

# Function to copy files to remote
remote_copy() {
    local local_file="$1"
    local remote_path="$2"
    if [ "$USE_SSH_KEY" = true ]; then
        scp "$local_file" "${REMOTE_USER}@${REMOTE_HOST}:$remote_path"
    else
        sshpass -p "$REMOTE_PASSWORD" scp "$local_file" "${REMOTE_USER}@${REMOTE_HOST}:$remote_path"
    fi
}

# 1. Copy the fixed server.cjs file
echo -e "${BLUE}[>] Copying fixed server.cjs to remote server${NC}"
if [ -f "server.cjs" ]; then
    remote_copy "server.cjs" "/tmp/server.cjs"
    echo -e "${GREEN}[✓] server.cjs copied${NC}"
else
    echo -e "${RED}[!] server.cjs not found locally${NC}"
    exit 1
fi

# 2. Fix the remote deployment
echo -e "${BLUE}[>] Fixing remote deployment${NC}"
remote_exec "
    # Stop the service
    sudo systemctl stop focusbudget 2>/dev/null || true
    
    # Find the correct deployment directory
    cd '$REMOTE_PATH'
    if [ -d 'focusbudget' ]; then
        DEPLOY_DIR='$REMOTE_PATH/focusbudget'
    else
        # Find the most recent directory
        DEPLOY_DIR=\$(ls -td focusbudget* 2>/dev/null | head -1)
        if [ -n \"\$DEPLOY_DIR\" ]; then
            DEPLOY_DIR='$REMOTE_PATH/\$DEPLOY_DIR'
        else
            echo 'No deployment directory found'
            exit 1
        fi
    fi
    
    echo \"Using deployment directory: \$DEPLOY_DIR\"
    
    # Copy the fixed server.cjs
    cp /tmp/server.cjs \"\$DEPLOY_DIR/server.cjs\"
    rm -f /tmp/server.cjs
    
    # Update the systemd service to point to the correct directory
    sudo tee /etc/systemd/system/focusbudget.service > /dev/null << EOF
[Unit]
Description=FocusBudget Personal Budget Management Application
After=network.target

[Service]
Type=simple
User=$REMOTE_USER
WorkingDirectory=\$DEPLOY_DIR
ExecStart=/usr/bin/node \$DEPLOY_DIR/server.cjs
Restart=on-failure
RestartSec=10
Environment=NODE_ENV=production
Environment=PORT=3001
Environment=DATA_DIR=\$DEPLOY_DIR/data
Environment=LOG_DIR=\$DEPLOY_DIR/logs
EnvironmentFile=\$DEPLOY_DIR/.env

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=\$DEPLOY_DIR/data
ReadWritePaths=\$DEPLOY_DIR/logs

[Install]
WantedBy=multi-user.target
EOF

    # Reload systemd and start the service
    sudo systemctl daemon-reload
    sudo systemctl enable focusbudget
    sudo systemctl start focusbudget
    
    # Wait a moment and check status
    sleep 3
    sudo systemctl status focusbudget --no-pager
"

echo -e "\n${GREEN}[✓] Service fix completed${NC}"
echo -e "${YELLOW}[i] Check the service status above${NC}"
echo -e "${YELLOW}[i] If successful, the service should be running without errors${NC}"

# 3. Test the deployment
echo -e "\n${BLUE}[>] Testing the deployment${NC}"
if remote_exec "curl -s http://localhost:3001/api/status" >/dev/null 2>&1; then
    echo -e "${GREEN}[✓] Service is responding${NC}"
    echo -e "${GREEN}[✓] FocusBudget should be accessible at http://${REMOTE_HOST}${NC}"
else
    echo -e "${YELLOW}[i] Service may still be starting up${NC}"
    echo -e "${YELLOW}[i] Check logs with: ssh ${REMOTE_USER}@${REMOTE_HOST} 'sudo journalctl -u focusbudget -f'${NC}"
fi

echo -e "\n${GREEN}==============================="
echo -e " Fix Complete!"
echo -e "===============================${NC}"
