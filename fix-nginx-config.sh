#!/bin/bash

# FocusBudget Nginx Configuration Fix Script
# This script manually sets up or fixes the Nginx reverse proxy configuration

# Colors for better readability
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

# Configuration
REMOTE_HOST="**********"
REMOTE_USER="labmaster"
REMOTE_TARGET="${REMOTE_USER}@${REMOTE_HOST}"

echo -e "\n${BLUE}==============================="
echo -e " FocusBudget Nginx Configuration Fix"
echo -e "===============================${NC}\n"

# Function to execute SSH commands (assumes SSH is already set up)
ssh_exec() {
  ssh "$REMOTE_TARGET" "$@"
}

echo -e "${BLUE}[>] Setting up Nginx reverse proxy configuration${NC}"

# Create the Nginx configuration
NGINX_CONFIG='# FocusBudget Nginx Configuration
# Reverse proxy from port 80 to Node.js backend on port 3001

server {
    listen 80;
    listen [::]:80;
    server_name _;

    # Security headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy strict-origin-when-cross-origin always;

    # Hide server information
    server_tokens off;

    # Increase client max body size for CSV uploads
    client_max_body_size 10M;

    # Main application proxy - forwards everything to Node.js backend
    location / {
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;

        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
    }

    # API specific settings with longer timeouts
    location /api/ {
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Extended timeout for processing
        proxy_connect_timeout 120s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;

        # Disable buffering for API responses
        proxy_buffering off;
    }

    # Health check endpoint
    location /health {
        proxy_pass http://127.0.0.1:3001/api/status;
        proxy_set_header Host $host;
        access_log off;
    }

    # Static assets with caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://127.0.0.1:3001;
        proxy_set_header Host $host;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}'

echo -e "${YELLOW}[i] Installing/updating Nginx if needed${NC}"
ssh_exec "
  # Install Nginx if not present
  if ! command -v nginx >/dev/null 2>&1; then
    echo 'Installing Nginx...'
    sudo apt-get update
    sudo apt-get install -y nginx
  else
    echo 'Nginx is already installed'
  fi
"

echo -e "${YELLOW}[i] Creating sites directories${NC}"
ssh_exec "
  sudo mkdir -p /etc/nginx/sites-available
  sudo mkdir -p /etc/nginx/sites-enabled
"

echo -e "${YELLOW}[i] Creating FocusBudget Nginx configuration${NC}"
echo "$NGINX_CONFIG" | ssh_exec "$REMOTE_TARGET" "sudo tee /etc/nginx/sites-available/focusbudget > /dev/null"

echo -e "${YELLOW}[i] Disabling default site${NC}"
ssh_exec "
  if [ -f /etc/nginx/sites-enabled/default ]; then
    echo 'Removing default site configuration'
    sudo rm -f /etc/nginx/sites-enabled/default
  fi
"

echo -e "${YELLOW}[i] Enabling FocusBudget site${NC}"
ssh_exec "
  sudo ln -sf /etc/nginx/sites-available/focusbudget /etc/nginx/sites-enabled/focusbudget
"

echo -e "${YELLOW}[i] Testing Nginx configuration${NC}"
if ssh_exec "sudo nginx -t"; then
  echo -e "${GREEN}[✓] Nginx configuration is valid${NC}"
  
  echo -e "${YELLOW}[i] Restarting Nginx${NC}"
  ssh_exec "
    sudo systemctl enable nginx
    sudo systemctl restart nginx
  "
  
  # Wait a moment for Nginx to start
  sleep 2
  
  if ssh_exec "systemctl is-active --quiet nginx"; then
    echo -e "${GREEN}[✓] Nginx is running successfully${NC}"
    
    echo -e "\n${YELLOW}[i] Testing connectivity${NC}"
    ssh_exec "
      echo 'Testing port 80:'
      curl -s -I http://localhost/ | head -3 || echo 'Port 80 test failed'
      
      echo ''
      echo 'Testing health endpoint:'
      curl -s http://localhost/health || echo 'Health endpoint test failed'
      
      echo ''
      echo 'Checking what is listening on port 80:'
      sudo netstat -tlnp | grep ':80 '
    "
    
    echo -e "\n${GREEN}[✓] Nginx reverse proxy setup complete!${NC}"
    echo -e "${YELLOW}[i] FocusBudget should now be accessible at:${NC}"
    echo -e "${YELLOW}  • http://${REMOTE_HOST}${NC}"
    echo -e "${YELLOW}  • http://${REMOTE_HOST}/health${NC}"
    echo -e "${YELLOW}  • http://${REMOTE_HOST}/api/status${NC}"
    
  else
    echo -e "${RED}[!] Nginx failed to start${NC}"
    ssh_exec "sudo journalctl -u nginx --since '2 minutes ago' --no-pager"
  fi
  
else
  echo -e "${RED}[!] Nginx configuration test failed${NC}"
  ssh_exec "sudo nginx -t"
fi

echo -e "\n${BLUE}==============================="
echo -e " Configuration Complete"
echo -e "===============================${NC}"

echo -e "\n${YELLOW}Useful commands for troubleshooting:${NC}"
echo -e "${YELLOW}• Check Nginx status: ssh $REMOTE_TARGET 'sudo systemctl status nginx'${NC}"
echo -e "${YELLOW}• View Nginx logs: ssh $REMOTE_TARGET 'sudo journalctl -u nginx -f'${NC}"
echo -e "${YELLOW}• Test config: ssh $REMOTE_TARGET 'sudo nginx -t'${NC}"
echo -e "${YELLOW}• Restart Nginx: ssh $REMOTE_TARGET 'sudo systemctl restart nginx'${NC}"
echo -e "${YELLOW}• Check ports: ssh $REMOTE_TARGET 'sudo netstat -tlnp | grep LISTEN'${NC}"
